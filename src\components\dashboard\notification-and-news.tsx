import { CircleEllipsis } from "lucide-react";
import { formatDistanceToNow } from "date-fns";

const notifications = [
  { text: "Security Audit Uploaded", time: new Date() },
  { text: "Smart Address Connected", time: new Date(Date.now() - 5 * 60 * 1000) },
  { text: "Scan Completed", time: new Date(Date.now() - 60 * 60 * 1000) },
  { text: "New Team Member Added", time: new Date(Date.now() - 24 * 60 * 60 * 1000) },
  { text: "Reminder: Pending Audit", time: new Date(Date.now() - 24 * 60 * 60 * 1000) },
];

const news = [
  { text: "Platform Update v1.5 Released", time: new Date(Date.now() - 2 * 60 * 60 * 1000) },
  { text: "New Feature: Smart Contract Comparison", time: new Date(Date.now() - 24 * 60 * 60 * 1000) },
  { text: "Upcoming Downtime", time: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000) },
  { text: "Audit with Git Connect Coming Soon!", time: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) },
  { text: "Security Tip of the Week", time: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) },
];

const NotificationAndNews = () => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div className="rounded-lg">
        <div className="flex flex-row items-center justify-between">
          <h3 className="text-lg font-normal text-race-dark/50">Notification</h3>
          <CircleEllipsis className="h-5 w-5 rotate-90 text-race-dark/50" />
        </div>
        <div className="mt-4">
          <div className="space-y-4">
            {notifications.map((item, index) => (
              <div key={index} className="flex justify-between items-center p-1.5 rounded-lg border">
                <span className="text-sm text-race-dark/50">{item.text}</span>
                <span className="text-xs text-race-dark/50">{formatDistanceToNow(item.time, { addSuffix: true })}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
      <div className="rounded-lg">
        <div className="flex flex-row items-center justify-between">
          <h3 className="text-lg font-normal text-race-dark/50">News</h3>
          <CircleEllipsis className="h-5 w-5 rotate-90 text-race-dark/50" />
        </div>
        <div className="mt-4">
          <div className="space-y-4">
            {news.map((item, index) => (
              <div key={index} className="flex justify-between items-center p-1.5 rounded-lg border">
                <span className="text-sm text-race-dark/50">{item.text}</span>
                <span className="text-xs text-race-dark/50">{formatDistanceToNow(item.time, { addSuffix: true })}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationAndNews;
