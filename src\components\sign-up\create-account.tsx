import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { GithubIcon, GoogleIcon } from "@/icons";
import { useState } from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { GoogleLogin, useGoogleLogin } from "@react-oauth/google";
import { loginWithGoogle, registerUser, loginUser } from "@/api/auth.service";
import { useCustomMutation } from "@/hooks/use-custom-mutation";
import { useAuth } from "@/state-management/auth-context";

// Schemas for register and login
const registerSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    // .regex(
    //   /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
    //   "Password must contain at least one uppercase letter, one lowercase letter, and one number"
    // ),
});

const loginSchema = registerSchema.omit({ name: true });

// Form values type
interface FormValues {
  name?: string;
  email: string;
  password: string;
}

interface CreateAccountProps {
  onSubmit: (name: string, email: string, password: string) => void;
}

const CreateAccount = ({ onSubmit }: CreateAccountProps) => {
  const [isLogin, setIsLogin] = useState(false);
  const schema = isLogin ? loginSchema : registerSchema;
  const form = useForm<FormValues>({
    resolver: zodResolver(schema) as any,
    defaultValues: { name: "", email: "", password: "" },
  });

  const { login } = useAuth(); // Assuming you have a custom hook for auth context

  // React Query mutations with success and error handlers
  const { mutate: registerMutate, isPending: isRegistering } = useCustomMutation(registerUser, {
    onSuccess: (_data, vars) => onSubmit(vars.name!, vars.email, vars.password),
    onError: (error) => console.error("Registration failed:", error),
  });

  const { mutate: loginMutate, isPending: isLoggingIn } = useCustomMutation(loginUser, {
    onSuccess: (_data, vars) => onSubmit("", vars.email, vars.password),
    onError: (error) => console.error("Login failed:", error),
  });

  const { mutate: googleMutate, isPending: isGoogleLoggingIn } = useCustomMutation(loginWithGoogle, {
    onSuccess: (data, vars) => {
      console.log("Google login succeeded");
      // Set authentication state
      login();
      // Call onSubmit to handle navigation - using empty values since Google login doesn't need them
    },
    onError: (error) => console.error("Google login failed:", error),
  });

  const handleSubmit: SubmitHandler<FormValues> = (values) => {
    if (isLogin) {
      loginMutate({ email: values.email, password: values.password });
    } else {
      registerMutate({ name: values.name!, email: values.email, password: values.password });
    }
  };

  // // Initiate Google OAuth login and send token to backend
  // const googleLogin = useGoogleLogin({
  //   onSuccess: (response) => {
  //     // Send Google access token to backend
  //     googleMutate({ token: response.access_token });
  //   },
  //   onError: () => {
  //     console.error("Google login failed");
  //   },
  // });

  const isSubmitting = isLogin ? isLoggingIn : isRegistering;

  const handleGoogleLogin = (credentialResponse: any) => {
    const token = credentialResponse.credential;
    const payload = {
      token,
    };
    googleMutate(payload);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)}>
        <Card className="w-full max-w-lg shadow-xl">
          <CardHeader className="space-y-1 text-left">
            <CardTitle className="text-2xl">Create an account</CardTitle>
            <CardDescription className="text-foreground">Enter your email below to create your account</CardDescription>
          </CardHeader>
          <CardContent className="grid gap-4">
            <div className="grid grid-cols-2 gap-6">
              <Button type="button" variant="outline" size={"lg"}>
                <GithubIcon />
                Github
              </Button>
              {/* <Button type="button" variant="outline" size={"lg"} onClick={() => googleLogin()}>
                <GoogleIcon />
                Google
              </Button> */}
              <GoogleLogin
                onSuccess={handleGoogleLogin}
                onError={() => console.error("Google login failed")}
                text="continue_with"
              />
            </div>
            <div className="flex items-center w-full gap-2">
              <span className="flex-1 border-t" />
              <span className="px-2 text-muted-foreground/50 text-xs uppercase z-10">Or continue with</span>
              <span className="flex-1 border-t" />
            </div>
            {!isLogin && (
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name</FormLabel>
                    <FormControl>
                      <Input placeholder="John Doe" {...field} type="text" disabled={isSubmitting} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} type="email" disabled={isSubmitting} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <Input {...field} type="password" disabled={isSubmitting} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
          <CardFooter>
            <div className="grid w-full gap-4">
              <Button type="submit" className="w-full cursor-pointer" disabled={isSubmitting}>
                {isSubmitting ? "Submitting..." : isLogin ? "Sign in" : "Create account"}
              </Button>
              <div className="text-left text-sm text-foreground">
                {isLogin ? "Don't have an account? " : "Already have an account? "}
                <button
                  type="button"
                  onClick={() => setIsLogin(!isLogin)}
                  className="underline"
                  disabled={isSubmitting}
                >
                  {isLogin ? "Sign up" : "Sign in"}
                </button>
              </div>
            </div>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
};

export default CreateAccount;
