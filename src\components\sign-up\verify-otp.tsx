import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { DashboardLogo } from "@/assets/icons";
import { useCustomMutation } from "@/hooks/use-custom-mutation";
import { resendCode } from "@/api/auth.service";

interface VerifyOTPProps {
  onSubmit: (otp: string) => void;
  email: string;
  isPending: boolean;
}

export default function VerifyOTP({ onSubmit, email, isPending }: VerifyOTPProps) {
  const [value, setValue] = useState("");

  // Mutation for resending verification code
  const { mutate: resendCodeMutation, isPending: isResending } = useCustomMutation(
    resendCode,
    {
      onSuccess: () => console.log("Verification code resent"),
      onError: (err) => console.error("Resend code failed:", err),
    }
  );

  const handleResendCode = () => {
    resendCodeMutation({ email });
  };

  const handleSubmit = () => {
    if (value.length === 6) {
      onSubmit(value);
    }
  };

  return (
    <Card className="w-full max-w-lg shadow-xl">
      <CardHeader>
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <div className="py-2 block">
              <DashboardLogo width={40} height={40} />
            </div>
            <span className="font-semibold tracking-widest text-2xl">RACE</span>
          </div>
        </div>
        <div className="space-y-2">
          <h1 className="text-2xl font-bold text-primary">
            Create your account
          </h1>
          <p className="text-primary">{"We've sent you a passcode."}</p>
          <p className="text-primary">Please check your inbox at {email}</p>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex justify-start">
          <InputOTP maxLength={6} value={value} onChange={setValue} disabled={isPending}>
            {[...Array(6)].map((_, i) => (
              <InputOTPGroup key={i}>
                <InputOTPSlot index={i} />
              </InputOTPGroup>
            ))}
          </InputOTP>
        </div>
        <div className="text-left">
          <Button
            variant="link"
            className="p-0 h-auto text-sm underline cursor-pointer"
            onClick={handleResendCode}
            disabled={isResending || isPending}
          >
            {isResending ? "Resending..." : "Resend code"}
          </Button>
        </div>
        <Button
          onClick={handleSubmit}
          className="w-full cursor-pointer"
          size={"lg"}
          disabled={value.length !== 6 || isPending}
        >
          {isPending ? "Submitting..." : "Submit"}
        </Button>
      </CardContent>
    </Card>
  );
}
