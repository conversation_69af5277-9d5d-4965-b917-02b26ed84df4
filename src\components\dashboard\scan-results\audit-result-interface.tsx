import { <PERSON><PERSON><PERSON><PERSON> } from "@/assets/icons";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { VulnerabilityTypeAndCount, VulnerabilityInformation } from "./ui";

// Sample blockchain vulnerability data
const vulnerabilities = [
  {
    id: 1,
    title: "Reentrancy in `withdraw` function",
    description: "The `withdraw` function sends Ether to an external address using `msg.sender.call.value()()` before updating the user's balance. This can lead to a reentrancy attack where the attacker repeatedly calls the `withdraw` function before the balance is updated, draining the contract's funds. The vulnerable code is `msg.sender.call.value(amount)()`.",
    filesAffected: ["contracts/Vulnerable.sol"],
    type: "critical" as const,
    label: "SWC-107"
  },
  {
    id: 2,
    title: "Integer Overflow in `transfer` function",
    description: "The `transfer` function does not use `SafeMath` for arithmetic operations. An integer overflow can occur in the line `balances[msg.sender] -= _value;` if `_value` is very large, allowing an attacker to manipulate their balance. The vulnerable variable is `balances`.",
    filesAffected: ["contracts/Token.sol"],
    type: "critical" as const,
    label: "SWC-101"
  },
  {
    id: 3,
    title: "Unchecked External Call in `claimReward`",
    description: "The return value of the external call `rewardToken.transfer(msg.sender, reward)` is not checked. If the `transfer` call fails, the user will not receive their reward, but the state of the contract will be updated as if they did. The vulnerable function is `claimReward`.",
    filesAffected: ["contracts/Staking.sol"],
    type: "medium" as const,
    label: "SWC-104"
  },
  {
    id: 4,
    title: "Timestamp Dependence in `Lottery` contract",
    description: "The `Lottery` contract uses `block.timestamp` to determine the winner. Miners can manipulate the timestamp of a block to their advantage, allowing them to influence the outcome of the lottery. The vulnerable code is `uint winner = uint(block.timestamp) % players.length;`.",
    filesAffected: ["contracts/Lottery.sol"],
    type: "medium" as const,
    label: "SWC-116"
  },
  {
    id: 5,
    title: "Use of Deprecated `tx.origin` for Authorization",
    description: "The contract uses `tx.origin` for authorization in the `onlyOwner` modifier. This can make the contract vulnerable to phishing attacks. An attacker can trick the owner into calling a malicious contract that then calls the vulnerable contract, gaining unauthorized access. The vulnerable code is `require(msg.sender == tx.origin, 'Owner only');`.",
    filesAffected: ["contracts/Owned.sol"],
    type: "low" as const,
    label: "SWC-115"
  },
  {
    id: 6,
    title: "Floating Pragma",
    description: "The contract uses a floating pragma `^0.8.0`. This can lead to unexpected behavior if the contract is compiled with a future version of the Solidity compiler that introduces breaking changes. It is recommended to use a fixed pragma, for example, `pragma solidity 0.8.20;`.",
    filesAffected: ["contracts/Vulnerable.sol", "contracts/Token.sol"],
    type: "informational" as const,
    label: "SWC-103"
  }
];

const AuditResultInterface = () => {
  return (
    <div className="space-y-6 flex flex-col">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-base font-medium">Scan Result</h1>
        <Button variant={"default"} className="bg-race-positive border hover:bg-race-positive/80">
          Completed
        </Button>
      </div>

      <p className="text-sm text-foreground">
        Uploaded zip files named{" "}
        <span className="font-medium">
          project node file <span>(124 kb)</span>
        </span>
      </p>

      <div className="flex gap-2 mr-auto justify-center items-center">
        <DashboardLogo width={40} height={40} className="mx-auto -mt-1" />
        <div className="h-8 w-px bg-border mx-2" />
        <div className="text-center">Audit Report</div>
      </div>

      {/* Summary Report */}
      <div className="finding_summary space-y-2.5">
        <h1 className="text-2xl font-semibold">Finding Summary</h1>
        <p className="text-xs ">The following issues were found during the audit:</p>
        <h2 className="text-xl font-normal">Technical Details</h2>

        <div className="critical_type_and_count">
          <VulnerabilityTypeAndCount title="Critical" count={2} />
          <div className="mt-3 space-y-3">
            {vulnerabilities
              .filter((v) => v.type === "critical")
              .map((vulnerability) => (
                <VulnerabilityInformation
                  key={vulnerability.id}
                  title={vulnerability.title}
                  description={vulnerability.description}
                  filesAffected={vulnerability.filesAffected}
                  type={vulnerability.type}
                  label={vulnerability.label}
                />
              ))}
          </div>
        </div>

        <div className="medium_type_and_count">
          <VulnerabilityTypeAndCount title="Medium" count={2} />
          <div className="mt-3 space-y-3">
            {vulnerabilities
              .filter((v) => v.type === "medium")
              .map((vulnerability) => (
                <VulnerabilityInformation
                  key={vulnerability.id}
                  title={vulnerability.title}
                  description={vulnerability.description}
                  filesAffected={vulnerability.filesAffected}
                  type={vulnerability.type}
                  label={vulnerability.label}
                />
              ))}
          </div>
        </div>

        <div className="low_type_and_count">
          <VulnerabilityTypeAndCount title="Low" count={1} />
          <div className="mt-3 space-y-3">
            {vulnerabilities
              .filter((v) => v.type === "low")
              .map((vulnerability) => (
                <VulnerabilityInformation
                  key={vulnerability.id}
                  title={vulnerability.title}
                  description={vulnerability.description}
                  filesAffected={vulnerability.filesAffected}
                  type={vulnerability.type}
                  label={vulnerability.label}
                />
              ))}
          </div>
        </div>

        <div className="informational_type_and_count">
          <VulnerabilityTypeAndCount title="Informational" count={1} />
          <div className="mt-3 space-y-3">
            {vulnerabilities
              .filter((v) => v.type === "informational")
              .map((vulnerability) => (
                <VulnerabilityInformation
                  key={vulnerability.id}
                  title={vulnerability.title}
                  description={vulnerability.description}
                  filesAffected={vulnerability.filesAffected}
                  type={vulnerability.type}
                  label={vulnerability.label}
                />
              ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuditResultInterface;
