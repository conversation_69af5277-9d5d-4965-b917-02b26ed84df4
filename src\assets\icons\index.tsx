export const DashboardLogo = ({
  strokeColor = "#505050",
  width = 32,
  height = 30,
  ...props
}) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 32 30"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_121_113)">
      <path
        d="M13.7957 10.0118H9.1901C8.89898 10.0117 8.61555 10.1034 8.38177 10.2732C8.14798 10.443 7.97625 10.682 7.89193 10.9548L4.86566 20.8138C4.8051 21.0114 4.79252 21.22 4.82897 21.4232C4.86542 21.6263 4.94985 21.8183 5.07556 21.9839C5.20127 22.1494 5.36475 22.2839 5.55298 22.3765C5.7412 22.4692 5.949 22.5176 6.15969 22.5177H8.95033C9.24145 22.5177 9.52483 22.4261 9.75861 22.2562C9.9924 22.0864 10.1641 21.8475 10.2484 21.5747L13.7957 10.0118ZM13.7957 10.0118L15.1104 5.74196C15.1939 5.46952 15.3649 5.23067 15.598 5.06081C15.8311 4.89096 16.1138 4.79913 16.4044 4.79896H23.4947C23.7059 4.79911 23.9142 4.84757 24.1029 4.94046C24.2916 5.03335 24.4555 5.16811 24.5816 5.334C24.7077 5.49989 24.7925 5.69232 24.8291 5.89595C24.8658 6.09958 24.8534 6.30879 24.7929 6.50689L24.0032 9.06878C23.9197 9.34123 23.7487 9.58008 23.5156 9.74994C23.2825 9.91979 22.9997 10.0116 22.7092 10.0118H13.7957Z"
        stroke={strokeColor}
        strokeWidth={1.20411}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M24.4872 7.48632H25.6366C25.8481 7.48562 26.0568 7.53351 26.2459 7.62615C26.4351 7.71879 26.5994 7.85359 26.7257 8.01972C26.852 8.18584 26.9367 8.37865 26.973 8.58264C27.0093 8.78663 26.9962 8.99611 26.9347 9.19424L26.1492 11.7561C26.0649 12.0289 25.8931 12.2679 25.6593 12.4377C25.4255 12.6075 25.1422 12.6992 24.851 12.6991H16.9463C16.6556 12.6987 16.3724 12.7902 16.1392 12.9602C15.906 13.1301 15.7352 13.3693 15.6523 13.6421L12.3903 24.262C12.306 24.5348 12.1343 24.7738 11.9005 24.9436C11.6667 25.1134 11.3833 25.2051 11.0922 25.205H8.30565C8.09466 25.2058 7.88646 25.158 7.69778 25.0656C7.50909 24.9731 7.34518 24.8386 7.2193 24.6729C7.09341 24.5071 7.00904 24.3147 6.97297 24.1112C6.9369 23.9077 6.95014 23.6987 7.01162 23.5012L7.31342 22.5096"
        stroke={strokeColor}
        strokeWidth={1.20411}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_121_113">
        <rect
          width="23.4"
          height="21.6"
          fill="white"
          transform="translate(4.2002 4.20001)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const UserProfileIcon = () => (
  <svg
    width="24"
    height="22"
    viewBox="0 0 24 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect x="0.799805" width="23" height="22" rx="11" fill="#F3D1D1" />
    <path
      d="M11.9717 15.0938C11.2998 15.0938 10.7139 14.9551 10.2139 14.6777C9.71777 14.4004 9.33301 14.0117 9.05957 13.5117C8.78613 13.0078 8.64941 12.418 8.64941 11.7422V6.36328H10.3252V11.8594C10.3252 12.2109 10.3936 12.5176 10.5303 12.7793C10.6709 13.0371 10.8643 13.2363 11.1104 13.377C11.3604 13.5176 11.6475 13.5879 11.9717 13.5879C12.4795 13.5879 12.8818 13.4297 13.1787 13.1133C13.4756 12.7969 13.624 12.3789 13.624 11.8594V6.36328H15.2998V11.7422C15.2998 12.418 15.1631 13.0078 14.8896 13.5117C14.6201 14.0117 14.2373 14.4004 13.7412 14.6777C13.2451 14.9551 12.6553 15.0938 11.9717 15.0938Z"
      fill="#DB6A6A"
    />
  </svg>
);

export const ExpandArrorIcon = () => (
  <svg
    width="10"
    height="14"
    viewBox="0 0 10 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M0.449893 8.74433C0.378493 8.81514 0.321851 8.89944 0.283176 8.99224C0.244501 9.08505 0.224609 9.18463 0.224609 9.28519C0.224609 9.38576 0.244501 9.48528 0.283176 9.57814C0.321851 9.67095 0.378493 9.75519 0.449893 9.826L4.25859 13.6346C4.32941 13.706 4.41364 13.7627 4.50645 13.8014C4.59932 13.8401 4.69883 13.86 4.7994 13.86C4.89997 13.86 4.99948 13.8401 5.09235 13.8014C5.18516 13.7627 5.26939 13.706 5.34021 13.6346L9.14891 9.826C9.22025 9.75519 9.27695 9.67095 9.31563 9.57814C9.35436 9.48528 9.37419 9.38576 9.37419 9.28519C9.37419 9.18463 9.35436 9.08505 9.31563 8.99224C9.27695 8.89944 9.22025 8.81514 9.14891 8.74433C9.07803 8.67299 8.9938 8.61629 8.90105 8.57761C8.80818 8.53894 8.70861 8.51904 8.60804 8.51904C8.50748 8.51904 8.40796 8.53894 8.31509 8.57761C8.22234 8.61629 8.13805 8.67299 8.06723 8.74433L4.7994 12.0198L1.53157 8.74433C1.46075 8.67299 1.37652 8.61629 1.28371 8.57761C1.19084 8.53894 1.09133 8.51904 0.990759 8.51904C0.890193 8.51904 0.790618 8.53894 0.697809 8.57761C0.605001 8.61629 0.520709 8.67299 0.449893 8.74433ZM4.25859 0.365327L0.449893 4.17397C0.378901 4.24496 0.322551 4.32931 0.284109 4.42212C0.245668 4.51493 0.225893 4.61433 0.225893 4.71478C0.225893 4.91766 0.306451 5.1122 0.449893 5.25564C0.520943 5.32664 0.605234 5.38299 0.698043 5.42148C0.790851 5.45987 0.890309 5.47964 0.990759 5.47964C1.19358 5.47964 1.38813 5.39909 1.53157 5.25564L4.7994 1.98017L8.06723 5.25564C8.13805 5.32699 8.22234 5.38369 8.31509 5.42236C8.40796 5.46109 8.50753 5.48099 8.60804 5.48099C8.70855 5.48099 8.80818 5.46109 8.90099 5.42236C8.9938 5.38369 9.07803 5.32699 9.14891 5.25564C9.22025 5.18477 9.27695 5.10054 9.31563 5.00778C9.35436 4.91492 9.37419 4.81534 9.37419 4.71478C9.37419 4.61421 9.35436 4.51469 9.31563 4.42183C9.27695 4.32908 9.22025 4.24484 9.14891 4.17397L5.34021 0.365327C5.26939 0.293926 5.18516 0.237286 5.09235 0.198552C4.99948 0.159877 4.89997 0.139985 4.7994 0.139985C4.69883 0.139985 4.59932 0.159877 4.50645 0.198552C4.41364 0.237286 4.32941 0.293926 4.25859 0.365327Z"
      fill="#505050"
    />
  </svg>
);

export const DashboardIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_123_1234)">
      <path
        d="M4 13H10C10.55 13 11 12.55 11 12V4C11 3.45 10.55 3 10 3H4C3.45 3 3 3.45 3 4V12C3 12.55 3.45 13 4 13ZM4 21H10C10.55 21 11 20.55 11 20V16C11 15.45 10.55 15 10 15H4C3.45 15 3 15.45 3 16V20C3 20.55 3.45 21 4 21ZM14 21H20C20.55 21 21 20.55 21 20V12C21 11.45 20.55 11 20 11H14C13.45 11 13 11.45 13 12V20C13 20.55 13.45 21 14 21ZM13 4V8C13 8.55 13.45 9 14 9H20C20.55 9 21 8.55 21 8V4C21 3.45 20.55 3 20 3H14C13.45 3 13 3.45 13 4Z"
        fill="#505050"
        fill-opacity="0.8"
      />
    </g>
    <defs>
      <clipPath id="clip0_123_1234">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const TeamIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1 19.25V16C1 15.5167 1.17084 15.1041 1.5125 14.7625C1.85417 14.4208 2.26666 14.25 2.75 14.25H6.275C6.55835 14.25 6.82915 14.3208 7.0875 14.4625C7.34585 14.6041 7.56665 14.8 7.75 15.05C8.23335 15.75 8.85 16.2916 9.6 16.675C10.35 17.0583 11.15 17.25 12 17.25C12.85 17.25 13.65 17.0583 14.4 16.675C15.15 16.2916 15.775 15.75 16.275 15.05C16.4583 14.8 16.675 14.6041 16.925 14.4625C17.175 14.3208 17.4417 14.25 17.725 14.25H21.25C21.7334 14.25 22.1458 14.4208 22.4875 14.7625C22.8291 15.1041 23 15.5167 23 16V19.25C23 19.4667 22.9291 19.6459 22.7875 19.7875C22.6459 19.9292 22.4667 20 22.25 20H17.25C17.0333 20 16.8541 19.9292 16.7125 19.7875C16.5708 19.6459 16.5 19.4667 16.5 19.25V17.025C15.9 17.575 15.2125 18 14.4375 18.3C13.6625 18.6 12.85 18.75 12 18.75C11.15 18.75 10.3417 18.6 9.575 18.3C8.80835 18 8.11665 17.575 7.5 17.025V19.25C7.5 19.4667 7.42915 19.6459 7.2875 19.7875C7.14585 19.9292 6.96665 20 6.75 20H1.75C1.53333 20 1.35416 19.9292 1.2125 19.7875C1.07083 19.6459 1 19.4667 1 19.25ZM12 15.75C11.4166 15.75 10.8541 15.6125 10.3125 15.3375C9.77085 15.0625 9.33335 14.6833 9 14.2C8.73335 13.8166 8.4125 13.5083 8.0375 13.275C7.6625 13.0416 7.25835 12.8834 6.825 12.8C7.30835 12.3 8.06665 11.9166 9.1 11.65C10.1334 11.3833 11.1 11.25 12 11.25C12.9 11.25 13.8709 11.3833 14.9125 11.65C15.9542 11.9166 16.7167 12.3 17.2 12.8C16.7667 12.8834 16.3625 13.0416 15.9875 13.275C15.6125 13.5083 15.2916 13.8166 15.025 14.2C14.6916 14.6833 14.2541 15.0625 13.7125 15.3375C13.1708 15.6125 12.6 15.75 12 15.75ZM4 12.5C3.25 12.5 2.60417 12.2291 2.0625 11.6875C1.52083 11.1459 1.25 10.5 1.25 9.75C1.25 8.98335 1.52083 8.33335 2.0625 7.8C2.60417 7.26665 3.25 7 4 7C4.76666 7 5.41665 7.26665 5.95 7.8C6.48335 8.33335 6.75 8.98335 6.75 9.75C6.75 10.5 6.48335 11.1459 5.95 11.6875C5.41665 12.2291 4.76666 12.5 4 12.5ZM20 12.5C19.25 12.5 18.6042 12.2291 18.0625 11.6875C17.5208 11.1459 17.25 10.5 17.25 9.75C17.25 8.98335 17.5208 8.33335 18.0625 7.8C18.6042 7.26665 19.25 7 20 7C20.7666 7 21.4167 7.26665 21.95 7.8C22.4834 8.33335 22.75 8.98335 22.75 9.75C22.75 10.5 22.4834 11.1459 21.95 11.6875C21.4167 12.2291 20.7666 12.5 20 12.5ZM12 9.5C11.25 9.5 10.6041 9.22915 10.0625 8.6875C9.52085 8.14585 9.25 7.5 9.25 6.75C9.25 5.98335 9.52085 5.33335 10.0625 4.8C10.6041 4.26667 11.25 4 12 4C12.7667 4 13.4166 4.26667 13.95 4.8C14.4833 5.33335 14.75 5.98335 14.75 6.75C14.75 7.5 14.4833 8.14585 13.95 8.6875C13.4166 9.22915 12.7667 9.5 12 9.5Z"
      fill="#505050"
      fill-opacity="0.8"
    />
  </svg>
);

export const ProjectIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M2 6.42857C2 5.51441 2.74653 4.77273 3.66667 4.77273H7C7.92014 4.77273 8.66667 5.51441 8.66667 6.42857V6.98052H15.3333V6.42857C15.3333 5.51441 16.0799 4.77273 17 4.77273H20.3333C21.2535 4.77273 22 5.51441 22 6.42857V9.74026C22 10.6544 21.2535 11.3961 20.3333 11.3961H17C16.0799 11.3961 15.3333 10.6544 15.3333 9.74026V9.18831H8.66667V9.74026C8.66667 9.79891 8.66319 9.85755 8.65625 9.91274L11.4444 13.6039H14.7778C15.6979 13.6039 16.4444 14.3456 16.4444 15.2597V18.5714C16.4444 19.4856 15.6979 20.2273 14.7778 20.2273H11.4444C10.5243 20.2273 9.77778 19.4856 9.77778 18.5714V15.2597C9.77778 15.2011 9.78125 15.1425 9.78819 15.0873L7 11.3961H3.66667C2.74653 11.3961 2 10.6544 2 9.74026V6.42857Z"
      fill="#505050"
      fill-opacity="0.8"
    />
  </svg>
);

export const NotificationIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M21.041 6.33333C21.041 7.21739 20.6811 8.06524 20.0404 8.69036C19.3997 9.31548 18.5307 9.66667 17.6247 9.66667C16.7186 9.66667 15.8497 9.31548 15.209 8.69036C14.5683 8.06524 14.2083 7.21739 14.2083 6.33333C14.2083 5.44928 14.5683 4.60143 15.209 3.97631C15.8497 3.35119 16.7186 3 17.6247 3C18.5307 3 19.3997 3.35119 20.0404 3.97631C20.6811 4.60143 21.041 5.44928 21.041 6.33333ZM13.1288 3.92933C12.1888 3.65162 11.1952 3.59252 10.2276 3.75678C9.26003 3.92103 8.34529 4.30408 7.55668 4.87524C6.76806 5.4464 6.12748 6.18981 5.68623 7.04592C5.24499 7.90203 5.01533 8.84706 5.01566 9.80533V14.3027C5.01566 14.4244 4.99109 14.5449 4.94336 14.6573C4.89564 14.7697 4.82568 14.8719 4.73749 14.9579C4.64929 15.044 4.5446 15.1122 4.42937 15.1588C4.31414 15.2054 4.19064 15.2293 4.06592 15.2293C3.7941 15.2293 3.53341 15.3347 3.3412 15.5222C3.149 15.7098 3.04102 15.9641 3.04102 16.2293C3.04102 16.4946 3.149 16.7489 3.3412 16.9364C3.53341 17.124 3.7941 17.2293 4.06592 17.2293H18.5498C18.8216 17.2293 19.0823 17.124 19.2745 16.9364C19.4667 16.7489 19.5747 16.4946 19.5747 16.2293C19.5747 15.9641 19.4667 15.7098 19.2745 15.5222C19.0823 15.3347 18.8216 15.2293 18.5498 15.2293C18.2979 15.2293 18.0564 15.1317 17.8783 14.9579C17.7001 14.7841 17.6001 14.5484 17.6001 14.3027V11.3333C16.712 11.3296 15.8401 11.1008 15.07 10.6692C14.2998 10.2377 13.6579 9.61823 13.207 8.87167C12.7562 8.1251 12.5119 7.27702 12.4982 6.41059C12.4845 5.54416 12.7018 4.68913 13.1288 3.92933ZM8.91712 20C8.91712 19.7348 9.0251 19.4804 9.2173 19.2929C9.40951 19.1054 9.6702 19 9.94202 19H12.6751C12.9469 19 13.2076 19.1054 13.3998 19.2929C13.592 19.4804 13.7 19.7348 13.7 20C13.7 20.2652 13.592 20.5196 13.3998 20.7071C13.2076 20.8946 12.9469 21 12.6751 21H9.94202C9.6702 21 9.40951 20.8946 9.2173 20.7071C9.0251 20.5196 8.91712 20.2652 8.91712 20Z"
      fill="#505050"
      fill-opacity="0.8"
    />
  </svg>
);

export const NewsIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M4.28571 4.6071C3.94472 4.6071 3.6177 4.74256 3.37658 4.98367C3.13546 5.2248 3 5.55182 3 5.89282V17.4642C3 17.9757 3.20319 18.4663 3.56487 18.828C3.92654 19.1897 4.41708 19.3928 4.92857 19.3928H18.75C19.3467 19.3928 19.919 19.1557 20.3409 18.7338C20.7629 18.3118 21 17.7395 21 17.1428V8.78567C21 8.43064 20.7121 8.14282 20.3571 8.14282H19.0959V16.4472C19.0959 16.8911 18.7361 17.2508 18.2923 17.2508C17.8485 17.2508 17.4887 16.8911 17.4887 16.4472V7.81291C17.4887 7.79965 17.489 7.78647 17.4896 7.77337V5.89282C17.4896 5.55182 17.3542 5.2248 17.113 4.98367C16.872 4.74256 16.5449 4.6071 16.2039 4.6071H4.28571ZM6.58268 8.18978C6.58268 7.83475 6.8705 7.54693 7.22554 7.54693H13.0113C13.3663 7.54693 13.6541 7.83475 13.6541 8.18978V11.4041C13.6541 11.7591 13.3663 12.0469 13.0113 12.0469H7.22554C6.8705 12.0469 6.58268 11.7591 6.58268 11.4041V8.18978ZM6.42197 15.5357C6.42197 15.0919 6.78174 14.7321 7.22554 14.7321H13.0113C13.4551 14.7321 13.8148 15.0919 13.8148 15.5357C13.8148 15.9795 13.4551 16.3392 13.0113 16.3392H7.22554C6.78174 16.3392 6.42197 15.9795 6.42197 15.5357Z"
      fill="#505050"
      fill-opacity="0.8"
    />
  </svg>
);

export const BillingsIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4.22222 5.33325C2.99653 5.33325 2 6.32978 2 7.55547V16.4444C2 17.6701 2.99653 18.6666 4.22222 18.6666H19.7778C21.0035 18.6666 22 17.6701 22 16.4444V7.55547C22 6.32978 21.0035 5.33325 19.7778 5.33325H4.22222ZM6.44444 16.4444H4.22222V14.2221C5.44792 14.2221 6.44444 15.2187 6.44444 16.4444ZM4.22222 9.7777V7.55547H6.44444C6.44444 8.78117 5.44792 9.7777 4.22222 9.7777ZM17.5556 16.4444C17.5556 15.2187 18.5521 14.2221 19.7778 14.2221V16.4444H17.5556ZM19.7778 9.7777C18.5521 9.7777 17.5556 8.78117 17.5556 7.55547H19.7778V9.7777ZM12 8.66659C14.566 8.66659 16.1698 11.4444 14.8868 13.6666C14.2913 14.6979 13.1909 15.3333 12 15.3333C9.434 15.3334 7.83027 12.5557 9.11317 10.3334C9.7086 9.30197 10.8091 8.66659 12 8.66659Z"
      fill="#505050"
      fill-opacity="0.5"
    />
  </svg>
);

export const SettingsIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4.10636 13.3043C3.96074 12.4278 3.96554 11.5474 4.10707 10.696C4.9878 10.7174 5.77989 10.2949 6.08774 9.55164C6.39559 8.80842 6.13429 7.94955 5.49638 7.34191C5.99832 6.63983 6.61752 6.01383 7.34026 5.49703C7.94793 6.1353 8.80706 6.39683 9.55048 6.0889C10.2939 5.78096 10.7165 4.98853 10.6948 4.10751C11.5713 3.9619 12.4518 3.9667 13.3032 4.10823C13.2818 4.98896 13.7043 5.78104 14.4476 6.0889C15.1907 6.39675 16.0496 6.13544 16.6573 5.49754C17.3594 5.99948 17.9854 6.61867 18.5022 7.34142C17.8639 7.94909 17.6024 8.80822 17.9103 9.55164C18.2182 10.2951 19.0106 10.7177 19.8917 10.696C20.0373 11.5725 20.0325 12.453 19.891 13.3043C19.0102 13.283 18.2181 13.7055 17.9103 14.4487C17.6024 15.1919 17.8637 16.0508 18.5016 16.6584C17.9997 17.3605 17.3805 17.9865 16.6578 18.5033C16.0501 17.8651 15.191 17.6035 14.4476 17.9114C13.7041 18.2194 13.2816 19.0118 13.3032 19.8928C12.4267 20.0385 11.5462 20.0337 10.6949 19.8921C10.7162 19.0114 10.2937 18.2193 9.55048 17.9114C8.80726 17.6036 7.94839 17.8649 7.34074 18.5028C6.63867 18.0009 6.01267 17.3817 5.49587 16.659C6.13414 16.0513 6.39567 15.1922 6.08774 14.4487C5.7798 13.7053 4.98738 13.2827 4.10636 13.3043ZM11.999 14.3996C13.3241 14.3996 14.3984 13.3253 14.3984 12.0002C14.3984 10.675 13.3241 9.60081 11.999 9.60081C10.6739 9.60081 9.59965 10.675 9.59965 12.0002C9.59965 13.3253 10.6739 14.3996 11.999 14.3996Z"
      fill="#505050"
      fill-opacity="0.8"
    />
  </svg>
);

export const ScanReportIcon = () => (
  <svg
    width="17"
    height="16"
    viewBox="0 0 17 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_121_132)">
      <path
        d="M6.7998 15H2.7998C2.53468 14.9997 2.2805 14.8942 2.09303 14.7068C1.90556 14.5193 1.80011 14.2651 1.7998 14V12C1.80011 11.7349 1.90556 11.4807 2.09303 11.2932C2.2805 11.1058 2.53468 11.0003 2.7998 11H6.7998C7.06493 11.0003 7.3191 11.1058 7.50658 11.2932C7.69405 11.4807 7.7995 11.7349 7.7998 12V14C7.7995 14.2651 7.69405 14.5193 7.50658 14.7068C7.3191 14.8942 7.06493 14.9997 6.7998 15ZM2.7998 12V14H6.7998V12H2.7998Z"
        fill="#505050"
      />
      <path
        d="M14.7998 10H6.7998C6.53468 9.9997 6.2805 9.89424 6.09303 9.70677C5.90556 9.5193 5.80011 9.26512 5.7998 9V7C5.80011 6.73488 5.90556 6.4807 6.09303 6.29323C6.2805 6.10576 6.53468 6.0003 6.7998 6H14.7998C15.0649 6.0003 15.3191 6.10576 15.5066 6.29323C15.694 6.4807 15.7995 6.73488 15.7998 7V9C15.7995 9.26512 15.694 9.5193 15.5066 9.70677C15.3191 9.89424 15.0649 9.9997 14.7998 10ZM6.7998 7V9H14.7998V7H6.7998Z"
        fill="#505050"
      />
      <path
        d="M8.7998 5H2.7998C2.53468 4.9997 2.2805 4.89424 2.09303 4.70677C1.90556 4.5193 1.80011 4.26512 1.7998 4V2C1.80011 1.73488 1.90556 1.4807 2.09303 1.29323C2.2805 1.10576 2.53468 1.0003 2.7998 1H8.7998C9.06493 1.0003 9.31911 1.10576 9.50658 1.29323C9.69405 1.4807 9.7995 1.73488 9.7998 2V4C9.7995 4.26512 9.69405 4.5193 9.50658 4.70677C9.31911 4.89424 9.06493 4.9997 8.7998 5ZM2.7998 2V4H8.7998V2H2.7998Z"
        fill="#505050"
      />
    </g>
    <defs>
      <clipPath id="clip0_121_132">
        <rect
          width="16"
          height="16"
          fill="white"
          transform="translate(0.799805)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const SearchIcon = () => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_121_139)">
      <path
        d="M5.56672 0.68593C6.85193 0.656725 8.09978 1.12384 9.04914 1.99062C9.99828 2.85729 10.5758 4.05695 10.6634 5.33925C10.7473 6.56856 10.3732 7.78283 9.6175 8.75136L12.9359 12.0687L12.0697 12.935L8.75227 9.61757C7.78377 10.3731 6.56938 10.7474 5.34016 10.6635C4.05776 10.5759 2.85825 9.99744 1.99153 9.04823C1.12474 8.09887 0.657633 6.85102 0.686838 5.56581C0.716166 4.28079 1.23888 3.05576 2.14778 2.14687C3.05671 1.23798 4.28167 0.715209 5.56672 0.68593ZM6.42219 1.99745C5.69237 1.85229 4.93584 1.92657 4.24836 2.21132C3.56095 2.49607 2.9733 2.97843 2.55989 3.59706C2.14652 4.21575 1.92612 4.94381 1.9261 5.68788L1.93098 5.87343C1.97803 6.80307 2.36735 7.68573 3.02864 8.34706C3.73388 9.05227 4.69048 9.44833 5.68781 9.4496C6.43191 9.4496 7.15993 9.22918 7.77864 8.81581C8.39725 8.4024 8.87964 7.81474 9.16438 7.12734C9.44913 6.43985 9.52341 5.68334 9.37824 4.95351C9.2331 4.22388 8.87489 3.55385 8.34895 3.02773C7.82275 2.50153 7.15204 2.14263 6.42219 1.99745Z"
        fill="#505050"
      />
    </g>
    <defs>
      <clipPath id="clip0_121_139">
        <rect width="14" height="14" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const PlusIcon = () => (
  <svg
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_121_148)">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M6.85714 0.857143C6.85714 0.383756 6.47338 0 6 0C5.52662 0 5.14286 0.383756 5.14286 0.857143V5.14286H0.857143C0.383756 5.14286 0 5.52662 0 6C0 6.47338 0.383756 6.85714 0.857143 6.85714H5.14286V11.1429C5.14286 11.6163 5.52662 12 6 12C6.47338 12 6.85714 11.6163 6.85714 11.1429V6.85714H11.1429C11.6163 6.85714 12 6.47338 12 6C12 5.52662 11.6163 5.14286 11.1429 5.14286H6.85714V0.857143Z"
        fill="#505050"
      />
    </g>
    <defs>
      <clipPath id="clip0_121_148">
        <rect width="12" height="12" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const MoreInfoIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_121_152)">
      <path
        d="M8.90477 11.92C8.90477 12.6164 8.15093 13.0516 7.54785 12.7034C6.94477 12.3552 6.94477 11.4848 7.54785 11.1366C7.68537 11.0572 7.84136 11.0154 8.00016 11.0154C8.49976 11.0154 8.90477 11.4204 8.90477 11.92ZM8.00016 3.77846C6.33717 3.77846 4.98477 4.99592 4.98477 6.49231V6.79384C4.98477 7.25809 5.48734 7.54825 5.88939 7.31612C6.07598 7.2084 6.19093 7.00931 6.19093 6.79384V6.49231C6.19093 5.66307 7.00282 4.98461 8.00016 4.98461C8.9975 4.98461 9.80939 5.66307 9.80939 6.49231C9.80939 7.32154 8.9975 8 8.00016 8C7.66708 7.99999 7.39708 8.26999 7.39708 8.60308V9.20616C7.39708 9.6704 7.89965 9.96056 8.3017 9.72843C8.48829 9.62071 8.60323 9.42161 8.60323 9.20616V9.15187C9.97825 8.89934 11.0155 7.80249 11.0155 6.49231C11.0155 4.99592 9.66314 3.77846 8.00016 3.77846ZM15.8402 8C15.8402 14.0352 9.30683 17.8073 4.08016 14.7896C-1.14651 11.772 -1.14651 4.22797 4.08016 1.21036C5.27199 0.522256 6.62395 0.16 8.00016 0.16C12.3282 0.164575 15.8356 3.67198 15.8402 8ZM14.634 8C14.634 2.89326 9.1058 -0.29845 4.68323 2.25492C0.26067 4.80829 0.26067 11.1917 4.68323 13.7451C5.69171 14.3273 6.83568 14.6338 8.00016 14.6338C11.6622 14.6297 14.6299 11.6621 14.634 8Z"
        fill="#505050"
      />
    </g>
    <defs>
      <clipPath id="clip0_121_152">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const DownwardArrow = () => (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M4.59873 9.03178C4.87574 8.73631 5.33982 8.72135 5.63528 8.99834L11.0004 14.0281L16.3655 8.99834C16.661 8.72135 17.125 8.73631 17.4021 9.03178C17.679 9.32726 17.664 9.79134 17.3687 10.0683L11.5019 15.5684C11.2199 15.8328 10.7809 15.8328 10.4988 15.5684L4.63217 10.0683C4.3367 9.79134 4.32174 9.32726 4.59873 9.03178Z"
      fill="#F5F5F5"
    />
  </svg>
);

export const SearchWhite = () => (
  <svg
    width="14"
    height="15"
    viewBox="0 0 14 15"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_60_1690)">
      <path
        d="M5.56672 1.18592C6.85193 1.15672 8.09978 1.62383 9.04914 2.49061C9.99828 3.35728 10.5758 4.55694 10.6634 5.83924C10.7473 7.06855 10.3732 8.28282 9.6175 9.25135L12.9359 12.5687L12.0697 13.4349L8.75227 10.1176C7.78377 10.8731 6.56938 11.2474 5.34016 11.1635C4.05776 11.0759 2.85825 10.4974 1.99153 9.54823C1.12474 8.59886 0.657633 7.35101 0.686838 6.06581C0.716166 4.78078 1.23888 3.55576 2.14778 2.64686C3.05671 1.73798 4.28167 1.2152 5.56672 1.18592ZM6.42219 2.49745C5.69237 2.35229 4.93584 2.42656 4.24836 2.71131C3.56095 2.99606 2.9733 3.47842 2.55989 4.09706C2.14652 4.71575 1.92612 5.4438 1.9261 6.18788L1.93098 6.37342C1.97803 7.30307 2.36735 8.18572 3.02864 8.84706C3.73388 9.55227 4.69048 9.94832 5.68781 9.94959C6.43191 9.94959 7.15993 9.72917 7.77864 9.31581C8.39725 8.90239 8.87964 8.31474 9.16438 7.62733C9.44913 6.93984 9.52341 6.18333 9.37824 5.4535C9.2331 4.72387 8.87489 4.05384 8.34895 3.52772C7.82275 3.00152 7.15204 2.64262 6.42219 2.49745Z"
        fill="#F5F5F5"
      />
    </g>
    <defs>
      <clipPath id="clip0_60_1690">
        <rect
          width="14"
          height="14"
          fill="white"
          transform="translate(0 0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const ExpansionIcon = () => (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7.33366 18.3334H3.66699M3.66699 18.3334V14.6667M3.66699 18.3334L9.16699 12.8334M14.667 3.66669H18.3337M18.3337 3.66669V7.33335M18.3337 3.66669L12.8337 9.16669"
      stroke="#F5F5F5"
      stroke-width="1.83329"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);
