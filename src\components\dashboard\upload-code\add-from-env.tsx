import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>ooter,
  DialogTitle,
  DialogDescription,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Upload } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

export interface AddFromEnvModalProps {
  onAdd?: (envText: string) => void;
}

export default function AddFromEnvModal({ onAdd }: AddFromEnvModalProps) {
  const [envText, setEnvText] = useState("");

  const handleAdd = () => {
    if (onAdd) onAdd(envText);
  };

  // File upload handler (optional, not implemented)
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    const reader = new FileReader();
    reader.onload = (event) => {
      setEnvText(event.target?.result as string);
    };
    reader.readAsText(file);
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Upload className="h-4 w-4 mr-1" /> Add from .env
        </Button>
      </DialogTrigger>
      <DialogContent className="bg-popover">
        <DialogHeader>
          <DialogTitle>Add from .env</DialogTitle>
          <DialogDescription className="text-foreground/80">
            Paste your .env contents to add multiple environment variables at once.
            <br />
            Check out the docs for correct syntax.
          </DialogDescription>
        </DialogHeader>
        <textarea
          className="w-full min-h-[100px] rounded-md border p-2 text-sm bg-muted resize-y"
          placeholder={"KEY_1 = VALUE_1\nKEY_2 = VALUE_2\nKEY_3 = VALUE_3"}
          value={envText}
          onChange={(e) => setEnvText(e.target.value)}
        />
        <div className="flex items-center gap-2 justify-end">
          <Label htmlFor="env-upload" className="flex items-center cursor-pointer">
            <Button variant="outline" size="sm" type="button" asChild className="bg-white">
              <span>
                Upload a file <Upload className="h-4 w-4 mr-1" />
              </span>
            </Button>
            <Input id="env-upload" type="file" accept=".env,.txt" className="hidden" onChange={handleFileUpload} />
          </Label>
        </div>
        <DialogFooter className="md:justify-start gap-4">
          <DialogClose asChild>
            <Button onClick={handleAdd}>Add variables</Button>
          </DialogClose>
          <DialogClose asChild>
            <Button variant="outline">Cancel</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
