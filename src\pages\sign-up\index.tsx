import { useState } from "react";
import CreateAccount from "@/components/sign-up/create-account";
import VerifyOTP from "@/components/sign-up/verify-otp";
import { useAuth } from "@/state-management/auth-context";
import { useNavigate } from "react-router";
import { useCustomMutation } from "@/hooks/use-custom-mutation";
import { sendVerificationEmail, verifyEmail } from "@/api/auth.service";

export default function SignUpPage() {
  // Custom step and data management
  const [currentStep, setCurrentStep] = useState(0);
  const [data, setData] = useState<{ [key: string]: any }>({});
  const { login } = useAuth();
  const navigate = useNavigate();

  // Email verification mutations
  const { mutate: sendCodeMutate, isPending: isSendingCode } = useCustomMutation(sendVerificationEmail, {
    onSuccess: () => {
      // Verification email sent successfully
      console.log("Verification email sent");
      nextStep();
    },
    onError: (err) => console.error("Send verification email failed:", err),
  });
  
  const { mutateAsync: verifyCodeMutateAsync, isPending: isVerifying } = useCustomMutation(verifyEmail, {
    onSuccess: () => {
      // After successful email verification, user is fully authenticated
      login(); // Update auth state
      navigate("/dashboard");
    },
    onError: (err) => console.error("Verify email failed:", err),
  });

  const setField = (field: string, value: any) => {
    setData((prev) => ({ ...prev, [field]: value }));
  };

  const nextStep = () => setCurrentStep((prev) => prev + 1);

  const handleCreateAccount = async (name: string, email: string, password: string) => {
    // If `name` is present, it's a registration flow
    if (name) {
      setField("name", name);
      setField("email", email);
      setField("password", password);
      // Registration is already handled in CreateAccount component
      // Now send verification email using the cookies set by registration
      // Adding a small delay to ensure cookies are set before the next request
      setTimeout(() => {
        sendCodeMutate({ email });
      }, 200);
    } else {
      // If `name` is not present, it's a login flow
      login();
      navigate("/dashboard");
    }
  };

  const handleVerifyOTP = async (otp: string) => {
    setField("otp", otp);
    // Verify the code - user is already registered, just verifying email
    await verifyCodeMutateAsync({ email: data.email as string, code: otp });
  };

  return (
    <>
      {currentStep === 0 && <CreateAccount onSubmit={handleCreateAccount} />}
      {currentStep === 1 && (
        <VerifyOTP
          onSubmit={handleVerifyOTP}
          email={data.email as string}
          isPending={isVerifying}
        />
      )}
    </>
  );
}
