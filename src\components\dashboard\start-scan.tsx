import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { DownwardArrow, ExpansionIcon, SearchWhite } from "@/assets/icons";

interface StartScanProps {
  onNext: () => void;
  onPrevious: () => void;
}

interface LogItem {
  time: string;
  timestamp: string;
  type: string;
  status: string;
}

const allLogs: LogItem[] = [
  {
    time: "Jun 20",
    timestamp: "02:32:05 PM",
    type: "Scan",
    status: "Initialised",
  },
  {
    time: "Jun 20",
    timestamp: "02:32:10 PM",
    type: "Dependency Check",
    status: "Running",
  },
  {
    time: "Jun 20",
    timestamp: "02:32:15 PM",
    type: "Static Analysis",
    status: "In Progress",
  },
  {
    time: "Jun 20",
    timestamp: "02:32:20 PM",
    type: "Audit Engine",
    status: "Detecting Issues",
  },
  {
    time: "Jun 20",
    timestamp: "02:32:25 PM",
    type: "Audit Engine",
    status: "Found 2 Critical, 1 Medium",
  },
  {
    time: "Jun 20",
    timestamp: "02:32:30 PM",
    type: "Summary",
    status: "Completed",
  },
];

export default function StartScan({ onNext, onPrevious }: StartScanProps) {
  const [logs, setLogs] = useState<LogItem[]>([]);

  useEffect(() => {
    let index = 0;
    const interval = setInterval(() => {
      if (index < allLogs.length) {
        setLogs((prev) => [...prev, allLogs[index]]);
        index++;
      } else {
        clearInterval(interval);
      }
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex flex-col h-full space-y-6 px-[50px] py-5">
      {/* Header */}
      <div className="flex items-center justify-between h-9">
        <h2 className="text-base font-bold">Start scan</h2>
        <Button
          variant="ghost"
          disabled
          className={cn(
            "h-9 px-3 text-base font-bold rounded-lg bg-[#76C58E] !opacity-100 text-white cursor-default border"
          )}
        >
          In Compilation
        </Button>
      </div>

      {/* Info */}
      <div className="text-base font-normal text-primary">
        Uploaded zip files named{" "}
        <span className="font-bold text-base text-primary">
          project node file (124 kb)
        </span>
      </div>

      {/* Log container */}
      <div className="flex flex-col flex-1 rounded-lg border overflow-hidden">
        {/* Top controls */}
        <div className="bg-primary flex items-center justify-between gap-x-2.5 px-4 border-b border-sidebar py-2">
          {/* Logs Button */}
          <Button
            variant="outline"
            size="sm"
            className="text-xs font-normal h-9 px-2 py-4.5 border border-sidebar bg-primary !text-sidebar hover:bg-primary/80 cursor-pointer"
          >
            All Logs
            <span className="ml-1">
              <DownwardArrow />
            </span>
          </Button>

          {/* Search box (grows to take rest of space) */}
          <div className="relative flex-1">
            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-sm text-sidebar">
              <SearchWhite />
            </span>
            <input
              type="text"
              placeholder="Search"
              className="text-sm text-sidebar border border-sidebar bg-primary rounded-lg pl-8 pr-3 py-2 w-full"
            />
          </div>

          {/* Timezone */}
          <div className="text-base font-normal text-sidebar border border-sidebar rounded-lg px-3 py-1.5">
            GMT+5:30
          </div>

          {/* Expand icon */}
          <div className="px-3 py-1.75 border border-sidebar rounded-lg">
            <ExpansionIcon />
          </div>
        </div>

        {/* Logs area */}
        <ScrollArea className="flex-1 bg-primary text-white px-6 py-4 text-sm font-medium">
          <div className="flex flex-col gap-2">
            {logs.map((log, index) => (
              <div
                className="grid grid-cols-[70px_120px_1fr_2fr] gap-4"
                key={index}
              >
                <span>{log.time}</span>
                <span>{log.timestamp}</span>
                <span>{log.type}</span>
                <span className="text-green-400">{log.status}</span>
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>

      {/* Footer Buttons */}
      <div className="flex justify-end gap-2 pt-4">
        <Button variant="outline" onClick={onPrevious}>
          Previous
        </Button>
        <Button onClick={onNext}>Next</Button>
      </div>
    </div>
  );
}
