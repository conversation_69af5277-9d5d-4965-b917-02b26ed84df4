export const GoogleIcon = ({ width = 25, height = 25, fill = "#505050" }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 25 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M23.0005 12.7331C23.0005 11.8698 22.9291 11.2398 22.7744 10.5864H12.7148V14.483H18.6196C18.5006 15.4514 17.8577 16.9097 16.4291 17.8897L16.4091 18.0202L19.5897 20.4349L19.8101 20.4564C21.8338 18.6247 23.0005 15.9297 23.0005 12.7331Z"
      fill={fill}
    />
    <path
      d="M12.714 23C15.6068 23 18.0353 22.0666 19.8092 20.4567L16.4282 17.8899C15.5235 18.5083 14.3092 18.9399 12.714 18.9399C9.88069 18.9399 7.47596 17.1083 6.61874 14.5766L6.49309 14.5871L3.18583 17.0954L3.14258 17.2132C4.90446 20.6433 8.5235 23 12.714 23Z"
      fill={fill}
    />
    <path
      d="M6.62095 14.5765C6.39476 13.9232 6.26386 13.2231 6.26386 12.4998C6.26386 11.7764 6.39476 11.0765 6.60905 10.4231L6.60306 10.284L3.25435 7.73535L3.14478 7.78642C2.41862 9.20977 2.00195 10.8081 2.00195 12.4998C2.00195 14.1915 2.41862 15.7897 3.14478 17.2131L6.62095 14.5765Z"
      fill={fill}
    />
    <path
      d="M12.7141 6.05997C14.7259 6.05997 16.083 6.91163 16.8569 7.62335L19.8807 4.73C18.0236 3.03834 15.6069 2 12.7141 2C8.52353 2 4.90447 4.35665 3.14258 7.78662L6.60686 10.4233C7.47598 7.89166 9.88073 6.05997 12.7141 6.05997Z"
      fill={fill}
    />
  </svg>
);

export const GithubIcon = ({ width = 25, height = 25, fill = "#505050" }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 25 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_22_27)">
      <path
        d="M12.5 0.796997C5.87 0.796997 0.5 6.17 0.5 12.797C0.5 18.1 3.938 22.597 8.705 24.182C9.305 24.295 9.525 23.924 9.525 23.605C9.525 23.32 9.515 22.565 9.51 21.565C6.172 22.289 5.468 19.955 5.468 19.955C4.922 18.57 4.133 18.2 4.133 18.2C3.046 17.456 4.217 17.471 4.217 17.471C5.422 17.555 6.055 18.707 6.055 18.707C7.125 20.542 8.864 20.012 9.55 19.705C9.658 18.929 9.967 18.4 10.31 18.1C7.645 17.8 4.844 16.768 4.844 12.17C4.844 10.86 5.309 9.79 6.079 8.95C5.944 8.647 5.539 7.427 6.184 5.774C6.184 5.774 7.189 5.452 9.484 7.004C10.444 6.737 11.464 6.605 12.484 6.599C13.504 6.605 14.524 6.737 15.484 7.004C17.764 5.452 18.769 5.774 18.769 5.774C19.414 7.427 19.009 8.647 18.889 8.95C19.654 9.79 20.119 10.86 20.119 12.17C20.119 16.78 17.314 17.795 14.644 18.09C15.064 18.45 15.454 19.186 15.454 20.31C15.454 21.916 15.439 23.206 15.439 23.596C15.439 23.911 15.649 24.286 16.264 24.166C21.065 22.592 24.5 18.092 24.5 12.797C24.5 6.17 19.127 0.796997 12.5 0.796997Z"
        fill={fill}
      />
    </g>
    <defs>
      <clipPath id="clip0_22_27">
        <rect
          width="24"
          height="24"
          fill="white"
          transform="translate(0.5 0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const ZipIcon = ({ width = 24, height = 24, fill = "#505050" }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_26_222)">
      <path
        d="M9.5 17.5C9.5 13.15 12.29 9.63 17.75 9.5C17.8114 9.50044 17.8709 9.47826 17.917 9.43769C17.9631 9.39711 17.9926 9.34098 18 9.28V5.21C17.9996 4.81231 17.8414 4.43104 17.56 4.15L13.85 0.44C13.569 0.158615 13.1877 0.000350104 12.79 0L1.5 0C1.10218 0 0.720644 0.158035 0.43934 0.43934C0.158035 0.720644 0 1.10218 0 1.5L0 20.5C0 20.8978 0.158035 21.2794 0.43934 21.5607C0.720644 21.842 1.10218 22 1.5 22H10.44C10.4841 21.9994 10.5272 21.9871 10.565 21.9644C10.6028 21.9418 10.634 21.9095 10.6553 21.8709C10.6766 21.8324 10.6873 21.7888 10.6864 21.7448C10.6855 21.7007 10.6729 21.6576 10.65 21.62C9.89509 20.3786 9.49717 18.953 9.5 17.5ZM6 18.5C5.80222 18.5 5.60888 18.4414 5.44443 18.3315C5.27998 18.2216 5.15181 18.0654 5.07612 17.8827C5.00043 17.7 4.98063 17.4989 5.01921 17.3049C5.0578 17.1109 5.15304 16.9327 5.29289 16.7929C5.43275 16.653 5.61093 16.5578 5.80491 16.5192C5.99889 16.4806 6.19996 16.5004 6.38268 16.5761C6.56541 16.6518 6.72159 16.78 6.83147 16.9444C6.94135 17.1089 7 17.3022 7 17.5C7 17.7652 6.89464 18.0196 6.70711 18.2071C6.51957 18.3946 6.26522 18.5 6 18.5ZM6 15C5.80222 15 5.60888 14.9414 5.44443 14.8315C5.27998 14.7216 5.15181 14.5654 5.07612 14.3827C5.00043 14.2 4.98063 13.9989 5.01921 13.8049C5.0578 13.6109 5.15304 13.4327 5.29289 13.2929C5.43275 13.153 5.61093 13.0578 5.80491 13.0192C5.99889 12.9806 6.19996 13.0004 6.38268 13.0761C6.56541 13.1518 6.72159 13.28 6.83147 13.4444C6.94135 13.6089 7 13.8022 7 14C7 14.2652 6.89464 14.5196 6.70711 14.7071C6.51957 14.8946 6.26522 15 6 15ZM6 11.5C5.80222 11.5 5.60888 11.4414 5.44443 11.3315C5.27998 11.2216 5.15181 11.0654 5.07612 10.8827C5.00043 10.7 4.98063 10.4989 5.01921 10.3049C5.0578 10.1109 5.15304 9.93275 5.29289 9.79289C5.43275 9.65304 5.61093 9.5578 5.80491 9.51921C5.99889 9.48063 6.19996 9.50043 6.38268 9.57612C6.56541 9.65181 6.72159 9.77998 6.83147 9.94443C6.94135 10.1089 7 10.3022 7 10.5C7 10.7652 6.89464 11.0196 6.70711 11.2071C6.51957 11.3946 6.26522 11.5 6 11.5ZM7.51 6.6C7.32055 6.81279 7.08782 6.98264 6.82741 7.09819C6.56699 7.21374 6.28489 7.27232 6 7.27C5.71511 7.27232 5.43301 7.21374 5.17259 7.09819C4.91218 6.98264 4.67945 6.81279 4.49 6.6C4.30436 6.39034 4.16483 6.14404 4.08042 5.87703C3.99601 5.61002 3.96861 5.32827 4 5.05L4.49 1.92C4.5073 1.79899 4.56921 1.68883 4.66357 1.61112C4.75793 1.53341 4.87792 1.49378 5 1.5H7C7.11931 1.50047 7.23468 1.54275 7.32603 1.61949C7.41739 1.69623 7.47895 1.80256 7.5 1.92L8 5C8.03872 5.28626 8.01496 5.57751 7.93038 5.85371C7.84579 6.12991 7.70238 6.38451 7.51 6.6Z"
        fill={fill}
      />
      <path
        d="M17.5001 11C19.2229 11.0027 20.8745 11.6881 22.0928 12.9062C23.3112 14.1247 23.9974 15.7769 24.0001 17.5C24.0001 18.7856 23.6186 20.0424 22.9044 21.1113C22.1902 22.1801 21.175 23.0129 19.9874 23.5049C18.7996 23.9969 17.4924 24.1258 16.2315 23.875C14.9708 23.6242 13.8123 23.0056 12.9034 22.0967C11.9945 21.1878 11.3759 20.0292 11.1251 18.7686C10.8743 17.5078 11.0023 16.2003 11.4942 15.0127C11.9862 13.825 12.8199 12.8099 13.8887 12.0957C14.9577 11.3815 16.2145 11 17.5001 11ZM14.5001 18.0996H16.4717V20.4805C16.472 21.0436 16.9291 21.5 17.4922 21.5C18.0553 21.4998 18.5115 21.0435 18.5118 20.4805V18.0996H20.3887L17.4444 13L14.5001 18.0996Z"
        fill={fill}
      />
    </g>
    <defs>
      <clipPath id="clip0_26_222">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ConnectSmartAddressIcon = (props: any) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="25"
    height="24"
    fill="none"
    viewBox="0 0 25 24"
    {...props}
  >
    <g clipPath="url(#clip0_26_254)">
      <path
        fill="#505050"
        fillRule="evenodd"
        d="M.551 3.171c0-.19.094-.484.505-.868s1.068-.78 1.967-1.135C4.815.46 7.363 0 10.233 0s5.418.46 7.21 1.168c.9.355 1.554.75 1.967 1.135.41.384.504.678.504.868 0 .191-.094.485-.504.868-.413.385-1.068.781-1.967 1.136-1.792.707-4.34 1.168-7.21 1.168s-5.418-.461-7.21-1.168c-.9-.355-1.555-.75-1.967-1.136-.41-.383-.505-.677-.505-.868m19.581 3.02c-.553.374-1.2.7-1.903.977-2.1.829-4.927 1.317-7.996 1.317s-5.897-.488-7.997-1.317a10 10 0 0 1-1.903-.977v4.43c.068.16.203.356.456.587.426.387 1.102.785 2.028 1.141 1.845.71 4.467 1.172 7.416 1.172q.78 0 1.524-.042a6.86 6.86 0 0 1 8.375-4.67zm-9.9 9.473q.38 0 .754-.01A3.84 3.84 0 0 0 10.19 18v2.99C4.743 20.982.333 19.085.333 16.749v-3.226c.512.312 1.091.587 1.715.827 2.147.826 5.041 1.315 8.185 1.315m8.102-2.378c-1.184 0-2.143.96-2.143 2.143v.857h4.285v-.857c0-1.184-.959-2.143-2.142-2.143m-4.715 2.143v.91c-.74.191-1.285.862-1.285 1.661v4.286c0 .947.767 1.714 1.714 1.714h8.571c.947 0 1.715-.767 1.715-1.714V18c0-.799-.547-1.47-1.286-1.66v-.911a4.714 4.714 0 1 0-9.429 0"
        clipRule="evenodd"
      ></path>
    </g>
    <defs>
      <clipPath id="clip0_26_254">
        <path fill="#fff" d="M.333 0h24v24h-24z"></path>
      </clipPath>
    </defs>
  </svg>
);

export const ConnectCodeIcon = (props: any) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="25"
    height="24"
    fill="none"
    viewBox="0 0 25 24"
    {...props}
  >
    <g fill="#505050" clipPath="url(#clip0_26_268)">
      <path d="M21.738 2.929c3.905 3.905 3.905 10.237 0 14.142l-1.205 1.204-4.162-4.161 1.069-1.069a4.114 4.114 0 1 0-5.818-5.818l-1.069 1.069-4.162-4.163L7.595 2.93c3.906-3.905 10.237-3.905 14.143 0M18.942 19.867l-4.162-4.162-1.069 1.068a4.114 4.114 0 0 1-5.817-5.818l1.068-1.068L4.8 5.724 3.595 6.93c-3.905 3.905-3.905 10.237 0 14.142 3.906 3.905 10.238 3.905 14.143 0z"></path>
      <path d="M15.848 11.454a1.864 1.864 0 0 0-2.636-2.636l-3.728 3.728a1.864 1.864 0 1 0 2.636 2.636z"></path>
    </g>
    <defs>
      <clipPath id="clip0_26_268">
        <path fill="#fff" d="M.667 0h24v24h-24z"></path>
      </clipPath>
    </defs>
  </svg>
);

export const DotsHorizontalIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    shapeRendering="geometricPrecision"
  >
    <circle cx="12" cy="12" r="11.5" stroke="#505050" strokeOpacity="0.5" />
    <path
      d="M11 8C11 7.44772 11.4477 7 12 7C12.5523 7 13 7.44772 13 8C13 8.55228 12.5523 9 12 9C11.4477 9 11 8.55228 11 8Z"
      fill="#1f1f1f"
    />
    <path
      d="M11 12C11 11.4477 11.4477 11 12 11C12.5523 11 13 11.4477 13 12C13 12.5523 12.5523 13 12 13C11.4477 13 11 12.5523 11 12Z"
      fill="#1f1f1f"
    />
    <path
      d="M11 16C11 15.4477 11.4477 15 12 15C12.5523 15 13 15.4477 13 16C13 16.5523 12.5523 17 12 17C11.4477 17 11 16.5523 11 16Z"
      fill="#1f1f1f"
    />
  </svg>
);

export const EditProfileIcon = ()=>(
  <svg width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.519531 17.1138C0.519531 7.94928 7.94882 0.519989 17.1133 0.519989C26.2778 0.519989 33.7071 7.94928 33.7071 17.1138C33.7071 26.2783 26.2778 33.7075 17.1133 33.7075C7.94882 33.7075 0.519531 26.2783 0.519531 17.1138Z" fill="#F5F5F5"/>
<path d="M17.1133 1.14011C25.9353 1.14011 33.0869 8.29168 33.0869 17.1137C33.0869 25.9358 25.9354 33.0874 17.1133 33.0874C8.29122 33.0874 1.13965 25.9358 1.13965 17.1137C1.13966 8.29169 8.29123 1.14012 17.1133 1.14011Z" stroke="#505050" stroke-opacity="0.5" stroke-width="1.24"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M25.3345 15.3758C26.2475 14.4628 26.602 13.1326 25.8878 12.057C25.5011 11.4747 24.9583 10.7837 24.201 10.0265C23.4438 9.26925 22.7528 8.72645 22.1705 8.33977C21.0948 7.62548 19.7647 7.98003 18.8517 8.89307L17.9145 9.8303C18.9131 10.4428 20.1449 11.3598 21.5063 12.7213C22.8677 14.0826 23.7847 15.3144 24.3972 16.313L25.3345 15.3758ZM20.1741 14.0535C18.7223 12.6017 17.4587 11.7252 16.5386 11.2062L8.6675 19.0773C8.32591 19.4189 8.0921 19.852 8.04562 20.3329C7.96693 21.1485 7.89445 22.6725 8.05994 25.0381C8.10227 25.643 8.58453 26.1252 9.18945 26.1676C11.555 26.3331 13.079 26.2606 13.8946 26.1819C14.3755 26.1354 14.8086 25.9016 15.1502 25.56L23.0213 17.6889C22.5023 16.7688 21.6258 15.5052 20.1741 14.0535Z" fill="#505050" fill-opacity="0.5"/>
</svg>

)