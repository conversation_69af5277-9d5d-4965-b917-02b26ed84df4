import {
  DashboardIcon,
  NewsIcon,
  NotificationIcon,
  ProjectIcon,
  TeamIcon,
  BillingsIcon,
  SettingsIcon,
} from "@/assets/icons";
import { LABEL, PATH } from "@/constants/path.constants";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils"; // Optional className utility
import { ScrollArea } from "@/components/ui/scroll-area";

// ✅ Updated type — allows section headers and optional properties
export interface SidebarLink {
  label?: string;
  link?: string;
  icon?: React.ReactNode;
  disabled?: boolean;
  comingSoon?: boolean;
  section?: string;
}

interface SidebarProps {
  activeLink: {
    label: string;
    path: string;
  };
  setActiveLink: (link: { label: string; path: string }) => void;
}

// ✅ Sidebar links array
const links: SidebarLink[] = [
  { label: LABEL.DASHBOARD, link: PATH.DASHBOARD, icon: <DashboardIcon /> },
  { label: LABEL.PROJECT, link: PATH.PROJECT, icon: <ProjectIcon /> },
  { label: LABEL.TEAM, link: PATH.TEAM, icon: <TeamIcon /> },
  {
    label: LABEL.NOTIFICATION,
    link: PATH.NOTIFICATION,
    icon: <NotificationIcon />,
  },
  { label: LABEL.NEWS, link: PATH.NEWS, icon: <NewsIcon /> },

  { section: "Organisation" },
  {
    label: "Billings",
    icon: <BillingsIcon />,
    disabled: true,
    comingSoon: true,
  },
  {
    label: LABEL.SETTINGS,
    link: PATH.SETTINGS,
    icon: <SettingsIcon />,
  },
];

export default function Sidebar({ activeLink, setActiveLink }: SidebarProps) {
  const navigate = useNavigate();

  const handleLinkClick = (link: SidebarLink) => {
    if (link.link && !link.disabled) {
      setActiveLink({ label: link.label || "", path: link.link });
      navigate(link.link);
    }
  };

  return (
    <div className="w-[285px] h-screen border-r bg-[#F5F5F5] flex flex-col">
      {/* Scrollable menu area */}
      <ScrollArea className="flex-1 px-8 py-4 pt-7.5">
        <nav className="flex flex-col gap-3">
          {links.map((link, idx) => {
            if (link.section) {
              return (
                <div
                  key={`section-${idx}`}
                  className="text-xs text-muted-foreground font-medium px-2 pt-12.5 pb-1 tracking-wide"
                >
                  {link.section}
                </div>
              );
            }

            const isActive = activeLink.path === link.link;
            const isDisabled = link.disabled;

            return (
              <Button
                key={`link-${idx}`}
                variant="ghost"
                disabled={isDisabled}
                className={cn(
                  "w-full justify-start -px-4 text-sm font-medium rounded-lg py-0 h-10",
                  isActive && "!bg-primary/10 text-primary font-semibold ",
                  isDisabled && "opacity-60 cursor-not-allowed"
                )}
                onClick={() => handleLinkClick(link)}
              >
                <span className="text-lg px-2.5">{link.icon}</span>
                <span className="flex items-center text-base font-bold text-primary/80">
                  {link.label}
                  {link.comingSoon && (
                    <span className="text-xs text-primary/50 ml-2 font-normal">
                      Coming soon
                    </span>
                  )}
                </span>
              </Button>
            );
          })}
        </nav>
      </ScrollArea>
    </div>
  );
}

export { links };
