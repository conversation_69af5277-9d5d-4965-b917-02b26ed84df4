// Auth Types
export interface RegisterUserRequest {
  email: string;
  password: string;
  name: string;
}

export interface LoginUserRequest {
  email: string;
  password: string;
}

export interface EmailRequest {
  email: string;
}

export interface ResetPasswordRequest {
  email: string;
  password: string;
  code: string;
}

export interface VerifyEmailRequest {
  email: string;
  code: string;
}

export interface GoogleLoginRequest {
  token: string;
}

// Project Types
export interface CreateProjectRequest {
  name: string;
  method: string;
}

// Build Types
export interface BuildConfig {
  compilationFramework: string;
  compilerVersion: string;
  repositoryUrl?: string;
  contractAddress?: string;
  networkName?: string;
}

export interface BuildRequest {
  zipFile: File;
  method: string;
  config: BuildConfig;
  projectId: string;
}

export interface SolidityLinesRequest {
  method: string;
  config: {
    contractAddress: string;
    networkName: string;
  };
}
