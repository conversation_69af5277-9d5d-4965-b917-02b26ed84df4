import { axiosRequest, getConfig } from "./axios";

export const createProject = async (body: { name: string; method: string }) => {
  const config = getConfig("POST", "/project", body);
  return axiosRequest(config);
};

export const getProjects = async () => {
  const config = getConfig("GET", "/project");
  return axiosRequest(config);
};

export const getProjectById = async (id: string) => {
  const config = getConfig("GET", `/project/${id}`);
  return axiosRequest(config);
};

export const updateProject = async (id: string, body: any) => {
  const config = getConfig("PUT", `/project/${id}`, body);
  return axiosRequest(config);
};
