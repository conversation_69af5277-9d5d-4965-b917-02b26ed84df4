import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import tailwindcss from "@tailwindcss/vite";

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    host: "0.0.0.0",
    port: 5173,
    strictPort: true,
    hmr: {
      clientPort: 443,
    },
    // Enable CORS for known hosts
    cors: true,
    allowedHosts: [".ngrok-free.app"],
    // Proxy API requests to avoid CORS errors in development
    proxy: {
      '/v1': {
        target: process.env.VITE_API_URL,
        changeOrigin: true,
        secure: false,
        cookieDomainRewrite: {
          '*': 'localhost'
        }
      }
    }
  },
});
