import { Table, <PERSON>Body, Table<PERSON>ell, TableHead, <PERSON>Header, TableRow } from "@/components/ui/table";
import { CircleEllipsis } from "lucide-react";

type Project = {
  name: string;
  createdBy: string;
  createdOn: string;
  latestVersion: string;
  lastScan: string;
  status: "Pending" | "Errors Found" | "Completed" | "Not Started";
};

const projects: Project[] = [
  {
    name: "Wallet Tracker",
    createdBy: "<EMAIL>",
    createdOn: "2025-01-22",
    latestVersion: "v2.0.0",
    lastScan: "Not scanned",
    status: "Pending",
  },
  {
    name: "NFT Dashboard",
    createdBy: "<EMAIL>",
    createdOn: "2025-03-04",
    latestVersion: "v0.9-beta",
    lastScan: "2025-06-10 09:00",
    status: "Errors Found",
  },
  {
    name: "DAO Portal",
    createdBy: "<EMAIL>",
    createdOn: "2024-11-15",
    latestVersion: "v3.4.1",
    lastScan: "2025-06-17 11:40",
    status: "Completed",
  },
  {
    name: "DApp Boilerplate",
    createdBy: "<EMAIL>",
    createdOn: "2025-02-01",
    latestVersion: "v1.0.0",
    lastScan: "Not scanned",
    status: "Not Started",
  },
  {
    name: "Voting System",
    createdBy: "<EMAIL>",
    createdOn: "2025-04-12",
    latestVersion: "v1.2.7",
    lastScan: "2025-06-15 18:05",
    status: "Completed",
  },
];

export function ProjectsList() {
  return (
    <div className="projects_list mt-7.5">
      <div className="flex justify-between items-center">
        <h2 className="text-xl mb-7.5">Projects List</h2>
        <CircleEllipsis className="h-5 w-5 rotate-90 text-race-dark/50" />
      </div>
      <Table className="bg-white">
        <TableHeader className="bg-white">
          <TableRow>
            <TableHead>Project Name</TableHead>
            <TableHead>Created By</TableHead>
            <TableHead>Created On</TableHead>
            <TableHead>Latest Version</TableHead>
            <TableHead>Last Scan</TableHead>
            <TableHead>Status</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody className="bg-dull-white">
          {projects.map((project) => (
            <TableRow key={project.name}>
              <TableCell>{project.name}</TableCell>
              <TableCell>{project.createdBy}</TableCell>
              <TableCell>{project.createdOn}</TableCell>
              <TableCell>{project.latestVersion}</TableCell>
              <TableCell>{project.lastScan}</TableCell>
              <TableCell>
                <p>{project.status}</p>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
