import { axiosRequest, getConfig } from "./axios";
import type { BuildRequest } from "./api.types";

export const buildProject = async (buildData: BuildRequest) => {
  const formData = new FormData();

  // Add the file
  formData.append("zipFile", buildData.zipFile);

  // Add method
  formData.append("method", buildData.method);

  // Add project ID
  formData.append("projectId", buildData.projectId);

  // Add config fields
  formData.append("config[compilationFramework]", buildData.config.compilationFramework);
  formData.append("config[compilerVersion]", buildData.config.compilerVersion);

  if (buildData.config.repositoryUrl) {
    formData.append("config[repositoryUrl]", buildData.config.repositoryUrl);
  }

  if (buildData.config.contractAddress) {
    formData.append("config[contractAddress]", buildData.config.contractAddress);
  }

  if (buildData.config.networkName) {
    formData.append("config[networkName]", buildData.config.networkName);
  }

  // Create config with multipart headers
  const config = getConfig("POST", "/build", formData);

  return axiosRequest(config);
};

export const getBuildById = async (id: string) => {
  const config = getConfig("GET", `/build/${id}`);
  return axiosRequest(config);
};

export const getProjectBuilds = async (projectId: string) => {
  const config = getConfig("GET", `/build/all/${projectId}`);
  return axiosRequest(config);
};

export const getBuildHTMLContentById = async (id: string) => {
  const config = getConfig("GET", `/build/html-content/${id}`);
  return axiosRequest(config);
};

export const getBuildReportById = async (id: string) => {
  const config = getConfig("GET", `/build/report/${id}`);
  return axiosRequest(config);
};

export const getBuildIssuesById = async (id: string) => {
  const config = getConfig("GET", `/build/issues/${id}`);
  return axiosRequest(config);
};

export const updateIssueStatus = async (issueId: string, body: any) => {
  const config = getConfig("PUT", `/build/issue/${issueId}`, body);
  return axiosRequest(config);
};

import type { SolidityLinesRequest } from "./api.types";

export const getTotalSolidityLines = async (data: SolidityLinesRequest) => {
  const formData = new FormData();

  // Add method
  formData.append("method", data.method);

  // Add config fields
  formData.append("config[contractAddress]", data.config.contractAddress);
  formData.append("config[networkName]", data.config.networkName);

  // Create config with multipart headers
  const config = getConfig("POST", "/build/total-solidity-lines", formData);

  return axiosRequest(config);
};
