import { CheckSquare2, Square } from "lucide-react";

export const VulnerabilityTypeAndCount = ({ title, count }: { title: string; count: number }) => {
  return (
    <div className="bg-dull-white border-race-positive border-2 p-2.5 rounded-lg flex justify-between">
      <p>{title}</p>
      <p>{count}</p>
    </div>
  );
};

const colorMap: Record<string, string> = {
  medium: "bg-race-positive",
  low: "bg-race-tentative",
  informational: "bg-race-informative",
  critical: "bg-red-400",
};

export const VulnerabilityLabel = ({ label, type }: { label: string; type: string }) => {
  return <div className={`${colorMap[type]} text-primary p-1 rounded-sm text-xs font-medium max-w-fit`}>{label}</div>;
};

interface VulnerabilityInformationProps {
  title: string;
  description: string;
  filesAffected: string[];
  type: "medium" | "low" | "informational" | "critical";
  label: string;
}

export const VulnerabilityInformation = ({
  title,
  description,
  filesAffected,
  type,
  label,
}: VulnerabilityInformationProps) => {
  const highlightVulnerabilities = (text: string) => {
    const parts = text.split(/(`[^`]*`)/g);
    return parts.map((part, index) => {
      if (part.startsWith("`") && part.endsWith("`")) {
        return (
          <span key={index} className="bg-primary/10 rounded-sm px-1">
            {part.slice(1, -1)}
          </span>
        );
      }
      return part;
    });
  };

  return (
    <div>
      <VulnerabilityLabel label={label} type={type} />
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold mt-2">{title}</h3>
        <div className="flex gap-2">
          <div className={`font-medium flex items-center text-foreground/80 border p-2 rounded-md`}>
            <CheckSquare2 className="inline mr-1" />
            mark as reviewed
          </div>
          <div className={`font-medium flex items-center border p-2 rounded-md bg-race-positive text-white`}>
            <Square className="inline mr-1" />
            resolved
          </div>
        </div>
      </div>
      <div className="mt-2 space-y-1">
        <p className="text-xs font-medium">Description:</p>
        <p className="text-sm">{highlightVulnerabilities(description)}</p>
        <p className="text-xs">Files Affected :</p>
        <ul className="list-disc list-inside">
          {filesAffected.map((file) => (
            <li className="text-xs text-primary/80" key={file}>
              {file}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};
