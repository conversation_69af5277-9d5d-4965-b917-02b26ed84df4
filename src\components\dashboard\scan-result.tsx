import { But<PERSON> } from "@/components/ui/button";
import AuditResultInterface from "./scan-results/audit-result-interface";
import { ScrollArea } from "../ui/scroll-area";

interface ScanResultProps {
  onPrevious: () => void;
  onRestart: () => void;
}

function ScanResult({ onPrevious, onRestart }: ScanResultProps) {
  return (
    <div className="space-y-4">
      <ScrollArea className="h-[calc(100vh-200px)] pr-4 px-[100px] py-5 ">
        <AuditResultInterface />
        <div className="flex justify-between mt-4">
          <Button variant="outline" onClick={onPrevious}>
            Previous
          </Button>
          <Button variant="default" onClick={onRestart}>
            Restart Scan
          </Button>
        </div>
      </ScrollArea>
    </div>
  );
}

export default ScanResult;
