import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DotsHorizontalIcon, EditProfileIcon } from "@/icons";

export default function SettingsPage() {
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [isOn, setIsOn] = useState(true);
  const [profile, setProfile] = useState({
    fullName: "Ankit Kumar",
    email: "<EMAIL>",
  });
  const [password, setPassword] = useState("Pedalsup@123&muchmore");

  const handleProfileChange = (e: any) => {
    setProfile((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  };

  return (
    <div className="py-7.5 px-12.5 space-y-10">
      {/* First Row: Profile & Project Preferences */}
      <div className="grid grid-cols-2 gap-4">
        {/* Profile Settings */}
        <div className="space-y-6 p-6">
          <div className="flex justify-between items-start mb-9">
            <h2 className="text-xl font-normal text-primary">
              Profile Settings
            </h2>
            <div className="w-6 h-6 cursor-pointer">
              <DotsHorizontalIcon />
            </div>
          </div>

          <div className="flex items-center gap-4 mb-9">
            <div className="relative">
              {/* <img
                src="https://i.pravatar.cc/100"
                alt="avatar"
                className="w-31 h-31 rounded-full border-2 border-white"
              /> */}
              <div className="w-30 h-30 rounded-full border-4 border-white overflow-hidden">
                <img
                  src="https://i.pravatar.cc/150?img=3"
                  alt="avatar"
                  className="w-full h-full object-cover"
                />
              </div>

              <div
                className="absolute bottom-2 right-0 w-8.5 h-8.5 bg-white rounded-full flex items-center object-cover justify-center border cursor-pointer"
                onClick={() => setIsEditingProfile((prev) => !prev)}
              >
                <EditProfileIcon />
              </div>
            </div>

            <div className="space-y-2 w-full">
              <div className="mb-4">
                <div className="text-sm font-medium text-muted-foreground leading-5">
                  Full name
                </div>
                {isEditingProfile ? (
                  <Input
                    name="fullName"
                    value={profile.fullName}
                    onChange={handleProfileChange}
                    className="text-sm font-medium text-primary/80 border-0 border-b border-b-primary/50 pb-1 leading-6"
                  />
                ) : (
                  <div className="text-sm font-medium text-primary/80 border-b border-b-primary/50 p-1.5 leading-6">
                    {profile.fullName}
                  </div>
                )}
              </div>
              <div>
                <div className="text-sm font-medium text-muted-foreground leading-5">
                  Email address
                </div>
                {isEditingProfile ? (
                  <Input
                    name="email"
                    value={profile.email}
                    onChange={handleProfileChange}
                    className="text-sm font-medium text-primary/80 border-0 border-b border-b-primary/50 pb-1 leading-6"
                  />
                ) : (
                  <div className="text-sm font-semibold text-primary/80 border-b border-b-primary/50 pb-1">
                    {profile.email}
                  </div>
                )}
              </div>
            </div>
          </div>

          <Button className="w-full bg-primary text-white font-semibold">
            Change Password
          </Button>

          <div className="flex gap-4">
            <Button className="flex-1 bg-primary text-white font-semibold">
              Save
            </Button>
            <Button variant="outline" className="flex-1 font-semibold">
              Cancel
            </Button>
          </div>
        </div>

        {/* Project Preferences */}
        <div className="p-6">
          <div className="flex justify-between items-start mb-7.5">
            <h2 className="text-xl font-normal">Project Preferences</h2>
            <div className="w-6 h-6 cursor-pointer">
              <DotsHorizontalIcon />
            </div>
          </div>

          <div className="flex flex-col gap-y-7.5">
            <div className="space-x-4 flex items-center">
              <label className="text-base font-normal text-primary/50 text-nowrap">
                Default git provider
              </label>
              <Select>
                <SelectTrigger className="w-full text-base font-bold text-primary">
                  <SelectValue placeholder="GitHub" />
                </SelectTrigger>

                <SelectContent>
                  <SelectItem value="github">GitHub</SelectItem>
                  <SelectItem value="gitlab">GitLab</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-1">
              <label className="text-base font-normal text-primary/50">
                Default contract address
              </label>
              <div className="flex gap-4 mt-4">
                <Input
                  type="password"
                  value={password}
                  className="flex-1 text-sm font-medium text-primary/80"
                  onChange={(e) =>
                    setPassword((e.target as HTMLInputElement).value)
                  }
                />
                <Button className="bg-primary text-white font-semibold h-10 ">
                  Change
                </Button>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm font-normal text-primary/50">
                Auto-scan on upload
              </span>
              <div className="flex items-center gap-2">
                {/* <Switch checked /> */}
                <button
                  onClick={() => setIsOn((prev) => !prev)}
                  className={`w-9 h-5 rounded-full flex items-center transition-colors duration-200 ${
                    isOn ? "bg-[#76C58E]" : "bg-gray-300"
                  }`}
                >
                  <span
                    className="h-4 w-4 rounded-full bg-white transition-transform duration-200"
                    style={{
                      transform: isOn ? "translateX(17px)" : "translateX(2px)",
                    }}
                  />
                </button>

                <span className="text-xl font-normal">On</span>
              </div>
            </div>

            <div className="flex space-y-1 pt-2 gap-4 items-center">
              <label className="text-base font-normal text-primary/50 text-nowrap">
                Default build environment
              </label>
              <Select>
                <SelectTrigger className="w-full text-base font-bold text-primary">
                  <SelectValue placeholder="Production" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="production">Production</SelectItem>
                  <SelectItem value="staging">Staging</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>

      {/* Second Row: Notification, Integrations, Account Settings */}
      <div className="grid grid-cols-3 gap-10">
        {/* Notification Preferences */}
        <div className="p-6">
          <div className="flex justify-between items-center mb-7.5">
            <h2 className="text-xl font-normal">Notification Preferences</h2>
            <div className="w-6 h-6 cursor-pointer">
              <DotsHorizontalIcon />
            </div>
          </div>

          <div className="flex flex-col space-y-3 gap-y-7.5">
            <div className="flex items-center gap-4">
              <button
                onClick={() => setIsOn((prev) => !prev)}
                className={`w-9 h-5 rounded-full flex items-center transition-colors duration-200 ${
                  isOn ? "bg-[#76C58E]" : "bg-gray-300"
                }`}
              >
                <span
                  className="h-4 w-4 rounded-full bg-white transition-transform duration-200"
                  style={{
                    transform: isOn ? "translateX(17px)" : "translateX(2px)",
                  }}
                />
              </button>

              <span className="text-sm">Scan Completed</span>
            </div>
            <div className="flex items-center gap-4">
              <button
                onClick={() => setIsOn((prev) => !prev)}
                className={`w-9 h-5 rounded-full flex items-center transition-colors duration-200 ${
                  isOn ? "bg-[#76C58E]" : "bg-gray-300"
                }`}
              >
                <span
                  className="h-4 w-4 rounded-full bg-white transition-transform duration-200"
                  style={{
                    transform: isOn ? "translateX(17px)" : "translateX(2px)",
                  }}
                />
              </button>

              <span className="text-sm">Error Found</span>
            </div>
            <div className="flex items-center gap-4">
              <button
                onClick={() => setIsOn((prev) => !prev)}
                className={`w-9 h-5 rounded-full flex items-center transition-colors duration-200 ${
                  isOn ? "bg-[#76C58E]" : "bg-gray-300"
                }`}
              >
                <span
                  className="h-4 w-4 rounded-full bg-white transition-transform duration-200"
                  style={{
                    transform: isOn ? "translateX(17px)" : "translateX(2px)",
                  }}
                />
              </button>

              <span className="text-sm">Scan Pending</span>
            </div>
          </div>
        </div>

        {/* Integrations */}
        <div className="flex flex-col gap-y-7.5 p-6">
          <div className="flex justify-between items-center ">
            <h2 className="text-xl font-normal">Integrations</h2>
            <div className="w-6 h-6 cursor-pointer">
              <DotsHorizontalIcon />
            </div>
          </div>

          <div className="flex items-center gap-2 relative">
            <Input
              value="0x456..............78G9L95"
              readOnly
              className="flex-1 cursor-default text-base font-bold text-primary/80"
            />
            <span className="px-2 py-1 rounded-md bg-muted absolute right-2 text-base font-bold text-primary/50">
              Connected
            </span>
          </div>

          <Button className="bg-primary w-full text-white font-semibold">
            Change
          </Button>
        </div>

        {/* Account Settings */}
        <div className="p-6 flex flex-col gap-y-7.5">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-normal">Account Settings</h2>
            <div className="w-6 h-6 cursor-pointer">
              <DotsHorizontalIcon />
            </div>
          </div>

          <Input
            value="Export Data"
            readOnly
            className="w-full text-center text-base font-bold text-primary/80 cursor-default"
          />

          <Button className="bg-[#DB6A6A] text-white w-full font-semibold">
            Delete Account
          </Button>
        </div>
      </div>
    </div>
  );
}
