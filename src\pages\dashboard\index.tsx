"use client";

import { useState } from "react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import UploadCode from "@/components/dashboard/upload-code";
import StartScan from "@/components/dashboard/start-scan";
import ScanResult from "@/components/dashboard/scan-result";
type Step = "upload" | "scan" | "result";

const steps = [
  { id: "upload", label: "Upload Code", component: UploadCode },
  { id: "scan", label: "Start Scan", component: StartScan },
  { id: "result", label: "Scan Result", component: ScanResult },
] as const;

export default function DashboardPage() {
  const [currentStep, setCurrentStep] = useState<Step>("upload");

  const currentStepIndex = steps.findIndex((step) => step.id === currentStep);

  const goToStep = (stepId: Step) => {
    setCurrentStep(stepId);
  };

  const goToNext = () => {
    const nextIndex = currentStepIndex + 1;
    if (nextIndex < steps.length) {
      setCurrentStep(steps[nextIndex].id);
    }
  };

  const goToPrevious = () => {
    const prevIndex = currentStepIndex - 1;
    if (prevIndex >= 0) {
      setCurrentStep(steps[prevIndex].id);
    }
  };

  const restart = () => {
    setCurrentStep("upload");
  };

  const renderCurrentComponent = () => {
    switch (currentStep) {
      case "upload":
        return <UploadCode onNext={goToNext} />;
      case "scan":
        return <StartScan onNext={goToNext} onPrevious={goToPrevious} />;
      case "result":
        return <ScanResult onPrevious={goToPrevious} onRestart={restart} />;
      default:
        return <UploadCode onNext={goToNext} />;
    }
  };

  return (
    <div className="min-h-screen">
      <div className="w-full px-[100px] py-5 border-b">
        {/* Main heading */}
        <h1 className="text-xl mb-7.5">
          Start an Audit with <span className="text-primary/50">Service</span>
        </h1>

        {/* Breadcrumb navigation */}
        <div>
          <Breadcrumb>
            <BreadcrumbList>
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <BreadcrumbItem>
                    {index === currentStepIndex ? (
                      // Current step
                      <BreadcrumbPage>{step.label}</BreadcrumbPage>
                    ) : (
                      // Clickable step
                      <BreadcrumbLink
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          goToStep(step.id);
                        }}
                      >
                        {step.label}
                      </BreadcrumbLink>
                    )}
                  </BreadcrumbItem>
                  {index < steps.length - 1 && <BreadcrumbSeparator />}
                </div>
              ))}
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </div>
      {/* Current step component */}
      <div className="transition-all duration-300 ease-in-out h-[calc(100vh-170px)] overflow-hidden">
        {renderCurrentComponent()}
      </div>
    </div>
  );
}
