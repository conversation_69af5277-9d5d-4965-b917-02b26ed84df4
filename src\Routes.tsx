import React from "react";
import { Navigate, useRoutes } from "react-router-dom";
import ProtectedLayout from "./layout/ProtectedLayout";
import AuthLayout from "./layout/AuthLayout";
import SignUpPage from "./pages/sign-up";
import { PATH } from "./constants/path.constants";
import DashboardPage from "./pages/dashboard";
import SettingsPage from "./pages/settings";
import { useAuth } from "./state-management/auth-context";
import ProjectPage from "./pages/project";
import TeamPage from "./pages/team";
import NotificationPage from "./pages/notification";
import NewsPage from "./pages/news";

const Routes: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth();

  // Show loading while checking for existing session
  if (isLoading) {
    return <div>Loading...</div>;
  }

  const element = useRoutes([
    {
      element: !isAuthenticated ? <AuthLayout /> : <Navigate to={PATH.DASHBOARD} />,
      children: [
        { path: "/", element: <Navigate to={PATH.SIGNUP} /> },
        { path: PATH.SIGNUP, element: <SignUpPage /> },
      ],
    },
    {
      element: isAuthenticated ? <ProtectedLayout /> : <Navigate to={PATH.SIGNUP} />,
      children: [
        { path: PATH.DASHBOARD, element: <DashboardPage /> },
        { path: PATH.PROJECT, element: <ProjectPage /> },
        { path: PATH.TEAM, element: <TeamPage /> },
        { path: PATH.NOTIFICATION, element: <NotificationPage /> },
        { path: PATH.NEWS, element: <NewsPage /> },
        { path: PATH.SETTINGS, element: <SettingsPage /> },
      ],
    },
  ]);

  return element;
};

export default Routes;
