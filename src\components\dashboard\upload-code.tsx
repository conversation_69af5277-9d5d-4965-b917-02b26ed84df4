import React, { useRef, useState } from "react";
import type { FC } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { ZipIcon, ConnectSmartAddressIcon, ConnectCodeIcon } from "@/icons";
import AuditWithCode from "./upload-code/audit-with-code";
import { ProjectsList } from "./projects-list";
import { ScrollArea } from "@/components/ui/scroll-area";
import NotificationAndNews from "./notification-and-news";

interface UploadCodeProps {
  onNext: () => void;
}

interface ActionCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  actionLabel: string;
  onAction: () => void;
  disabled?: boolean;
}

const ActionCard: FC<ActionCardProps> = ({ icon, title, description, actionLabel, onAction, disabled }) => (
  <Card className="flex flex-col bg-white rounded-lg border-2">
    <CardHeader className="gap-4">
      <CardTitle className="flex items-center gap-3">
        {icon}
        {title}
      </CardTitle>
      <CardDescription className="text-foreground/80">{description}</CardDescription>
    </CardHeader>
    <CardContent className="mt-auto">
      <Button onClick={onAction} disabled={disabled}>
        {actionLabel}
      </Button>
    </CardContent>
  </Card>
);

function UploadCode({ onNext }: UploadCodeProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [auditType, setAuditType] = useState<"none" | "zip" | "smart-address" | "git">("none");

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.name.toLowerCase().endsWith(".zip")) {
      setAuditType("zip");
    }
    e.target.value = "";
  };

  // Render different forms based on audit type
  if (auditType === "zip") {
    return <AuditWithCode onNext={onNext} type="zip" />;
  }

  if (auditType === "smart-address") {
    return <AuditWithCode onNext={onNext} type="smart-address" />;
  }

  return (
    <ScrollArea className="pr-4 h-[calc(100vh-200px)] px-[100px] py-5">
      <div>
        {/* hidden file input for zip upload */}
        <input type="file" accept=".zip" ref={fileInputRef} className="hidden" onChange={handleFileChange} />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-7.5">
          <ActionCard
            icon={<ZipIcon width={24} height={24} />}
            title="Audit with code zip"
            description="Submit a zipped version of your source code for automated security and performance auditing."
            actionLabel="Upload"
            onAction={() => fileInputRef.current?.click()}
          />
          <ActionCard
            icon={<ConnectSmartAddressIcon width={24} height={24} />}
            title="Audit with smart address"
            description="Provide a smart contract address to fetch and audit its verified source code on-chain."
            actionLabel="Connect"
            onAction={() => setAuditType("smart-address")}
          />
          <ActionCard
            icon={<ConnectCodeIcon width={24} height={24} />}
            title="Audit with git connect"
            description="Connect GitHub, GitLab, or Bitbucket to fetch and audit your codebase directly."
            actionLabel="Coming Soon"
            onAction={() => {}}
            disabled
          />
        </div>

        <div className="projects_list mt-10">
          <ProjectsList />
        </div>

        <div className="projects_list mt-10">
          <NotificationAndNews />
        </div>
      </div>
    </ScrollArea>
  );
}

export default UploadCode;
