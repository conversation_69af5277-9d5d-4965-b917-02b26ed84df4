@import "tailwindcss";
@import "tw-animate-css";
/* @import url("https://fonts.googleapis.com/css2?family=Reddit+Sans:ital,wght@0,200..900;1,200..900&family=Spline+Sans:wght@300..700&display=swap"); */

:root {
  font-family: "Reddit Sans", ui-sans-serif, system-ui, sans-serif;

  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  --radius: 0.625rem;
  --background: #f0f0f050;
  --foreground: #505050;
  --card: #f0f0f0;
  --card-foreground: #505050;
  --popover: #ffffff;
  --popover-foreground: #232323;
  --primary: #505050;
  --primary-foreground: #ffffff;
  --secondary: #ffffff;
  --secondary-foreground: #232323;
  --muted: #ffffff;
  --muted-foreground: #50505050;
  --accent: #f0f0f0;
  --accent-foreground: #333333;
  --destructive: #e0a3a3;
  --border: #50505080;
  --input: #d0d0d0;
  --ring: #b0b0b0;
  --chart-1: #a0a0a0;
  --chart-2: #9a9a9a;
  --chart-3: #6a6a6a;
  --chart-4: #d0d0d0;
  --chart-5: #f0f0f0;
  --sidebar: #f5f5f5;
  --sidebar-foreground: #232323;
  --sidebar-primary: #505050;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f0f0f0;
  --sidebar-accent-foreground: #333333;
  --sidebar-border: #d0d0d0;
  --sidebar-ring: #b0b0b0;
  --button-green :#76C58E;
  --race-positive: #76C58E;
  --race-tentative: #FFFCA4;
  --race-informative: #A4C9FF;
  --dull-white: #f5f5f5;
  --race-dark: #505050;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-button-green: var(--button-green);
  --color-race-positive: var(--race-positive);
  --color-dull-white: var(--dull-white);
  --color-race-tentative: var(--race-tentative);
  --color-race-informative: var(--race-informative);
  --color-race-dark: var(--race-dark);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    color-scheme: light;
  }
}
