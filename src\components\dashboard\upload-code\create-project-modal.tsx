import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON>Footer, DialogTitle, DialogDescription, DialogClose } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Plus } from "lucide-react";

export interface CreateProjectModalProps {
  onCreate?: (projectName: string, environmentName: string) => void;
}

export default function CreateProjectModal({ onCreate }: CreateProjectModalProps) {
  const [projectName, setProjectName] = useState("");
  const [environmentName, setEnvironmentName] = useState("Production");

  const handleCreate = () => {
    if (onCreate) {
      onCreate(projectName, environmentName);
    }
    // DialogClose will close the dialog via wrapping the create button
  };

  return (
    <Dialog>
      <DialogTrigger asChild >
        <Button variant="outline" size="sm" className="text-sm bg-transparent">
          <Plus className="h-4 w-4 mr-1" />
          Create a project
        </Button>
      </DialogTrigger>
      <DialogContent className="bg-popover">
        <DialogHeader>
          <DialogTitle>Create a project</DialogTitle>
          <DialogDescription className="text-foreground/80">Projects organize your services to make app development easier.</DialogDescription>
        </DialogHeader>
        <div className="grid gap-6 py-4">
          <div className="grid grid-row-4 items-center gap-4">
            <Label htmlFor="project-name" className="text-right">Project name</Label>
            <Input
              id="project-name"
              value={projectName}
              onChange={e => setProjectName(e.target.value)}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-row-4 items-center gap-6">
            <div>
              <Label htmlFor="environment-name" className="text-right">
              Environment name <span className="text-muted-foreground text-sm">(Optional)</span>
            </Label>
            <p className="text-foreground/80 text-xs">Setup an initial environment. You can add a new environment at any time.</p>
            </div>
            <Input
              id="environment-name"
              value={environmentName}
              onChange={e => setEnvironmentName(e.target.value)}
              className="col-span-3"
            />
          </div>
        </div>
        <DialogFooter className="md:justify-start">
          <DialogClose asChild>
            <Button onClick={handleCreate}>Create a project</Button>
          </DialogClose>
          <DialogClose asChild>
            <Button variant="outline">Cancel</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
