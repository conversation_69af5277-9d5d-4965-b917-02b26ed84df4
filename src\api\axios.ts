import axios from "axios";
import type { AxiosRequestConfig, AxiosResponse, Method } from "axios";

axios.defaults.withCredentials = true;

const BASE_URL = import.meta.env.VITE_API_URL;

type Headers = Record<string, string>;

export const getHeader = (): Headers => {
  const headers: Headers = {
    "Content-Type": "application/json",
    "ngrok-skip-browser-warning": "true",
    "Cache-Control": "no-cache",
    Pragma: "no-cache",
  };

  return headers;
};

export const getConfig = (
  method: Method,
  suffix: string = "",
  body?: unknown,
  params?: Record<string, unknown>,
  requestHeaders?: Headers,
  customBaseUrl?: string,
  isFormData: boolean = false,
): AxiosRequestConfig => {
  const headers = requestHeaders ?? getHeader();
  if (isFormData) {
    delete headers["Content-Type"];
  }

  const object: AxiosRequestConfig = {
    method: method,
    url: `${customBaseUrl || BASE_URL + suffix}`,
    headers: headers,
  };

  if (body) {
    object.data = body;
  }

  if (params) {
    object.params = params;
  }

  return object;
};

export const axiosRequest = <T = unknown>(config: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
  axios.request(config);
