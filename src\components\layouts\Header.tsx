import { useState } from "react";
import {
  DashboardLogo,
  ExpandArrorIcon,
  MoreInfoIcon,
  PlusIcon,
  ScanReportIcon,
  SearchIcon,
  UserProfileIcon,
} from "@/assets/icons";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";

const Header = () => {
  const [showSearch, setShowSearch] = useState(false);
  const [searchValue, setSearchValue] = useState("");

  return (
    <>
      <div className="h-[46px] px-5 flex w-full bg-secondary">
        {/* Left Section */}
        <div className="flex items-center">
          <div className="pr-5 py-2">
            <DashboardLogo />
          </div>
          <Separator orientation="vertical" />
          <div className="pl-5 pr-1.5 py-2 ">
            <UserProfileIcon />
          </div>
          <h1 className="flex items-center pr-5 text-base font-normal">
            User’s workspace
            <span className="pl-2.5">
              <ExpandArrorIcon />
            </span>
          </h1>
          <Separator orientation="vertical" />
        </div>

        {/* Middle Section */}
        <div className="flex items-center justify-between flex-1 px-5">
          <h1 className="flex items-center text-base font-normal ">
            <span className="pr-1.5">
              <ScanReportIcon />
            </span>
            Scan Report
          </h1>

          <div className="flex items-center gap-2">
            {!showSearch ? (
              <div
                onClick={() => setShowSearch(true)}
                className="flex items-center cursor-pointer"
              >
                <span className="pr-1.5">
                  <SearchIcon />
                </span>
                Search
              </div>
            ) : (
              <Input
                type="text"
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                placeholder="Search..."
                className="h-8 w-56 text-sm"
                onBlur={() => setShowSearch(false)} // optional: auto-hide on blur
                autoFocus
              />
            )}
          </div>
        </div>

        {/* Right Section */}
        <Separator orientation="vertical" />
        <div className="flex items-center pl-5">
          <div className="pr-1.5">
            <PlusIcon />
          </div>
          <h2 className="text-base font-normal">New</h2>
          <div className="mx-4">
            <MoreInfoIcon />
          </div>
          <div>
            <UserProfileIcon />
          </div>
        </div>
      </div>

      <Separator />
    </>
  );
};

export default Header;