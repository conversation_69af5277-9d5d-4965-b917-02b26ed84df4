import Header from "@/components/layouts/Header";
import Sidebar, { links } from "@/components/layouts/Sidebar";
import { useState } from "react";
import { Outlet, useLocation } from "react-router";

const ProtectedLayout = () => {
  const location = useLocation();
  const projectsCount = 1; // Placeholder for projects count, replace with actual logic if needed

  const linkInfo = links.find((link) => link.link === location.pathname);
  const { label = "", link: path = "" } = linkInfo || {};
  // const showSearch = linkInfo?.search;

  const [activeLink, setActiveLink] = useState({ label, path });

  return (
    <div className="flex h-screen">
      <div className="flex flex-col flex-1 main_container overflow-hidden">
        <Header />
        <div className="flex flex-1">
          {projectsCount > 0 && (
            <Sidebar activeLink={activeLink} setActiveLink={setActiveLink} />
          )}

          <div className="flex-1 overflow-y-auto page_container bg-white">
            <Outlet />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProtectedLayout;
