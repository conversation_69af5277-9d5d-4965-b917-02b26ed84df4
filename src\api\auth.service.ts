import { axiosRequest, getConfig } from "./axios";
import type {
  RegisterUserRequest,
  LoginUserRequest,
  EmailRequest,
  ResetPasswordRequest,
  VerifyEmailRequest,
  GoogleLoginRequest,
} from "./api.types";

export const registerUser = async (body: RegisterUserRequest): Promise<void> => {
  const config = getConfig("POST", "/auth/register", body);
  await axiosRequest(config);
};

export const loginUser = async (body: LoginUserRequest): Promise<void> => {
  const config = getConfig("POST", "/auth/login", body);
  await axiosRequest(config);
};

export const logoutUser = async (): Promise<void> => {
  const config = getConfig("POST", "/auth/logout");
  await axiosRequest(config);
};

export const forgotPassword = async (body: EmailRequest): Promise<void> => {
  const config = getConfig("POST", "/auth/forgot-password", body);
  await axiosRequest(config);
};

export const resetPassword = async (body: ResetPasswordRequest): Promise<void> => {
  const config = getConfig("POST", "/auth/reset-password", body);
  await axiosRequest(config);
};

export const sendVerificationEmail = async (body: EmailRequest): Promise<void> => {
  const config = getConfig("POST", "/auth/send-verification-email", body);
  await axiosRequest(config);
};

export const verifyEmail = async (body: VerifyEmailRequest): Promise<void> => {
  const config = getConfig("POST", "/auth/verify-email", body);
  await axiosRequest(config);
};

export const resendCode = async (body: EmailRequest): Promise<void> => {
  const config = getConfig("POST", "/auth/resend-code", body);
  await axiosRequest(config);
};

export const loginWithGoogle = async (body: GoogleLoginRequest): Promise<void> => {
  const config = getConfig("POST", "/auth/google-login", body);
  await axiosRequest(config);
};
