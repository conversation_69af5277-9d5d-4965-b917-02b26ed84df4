import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus, Trash2 } from "lucide-react";
import { useState } from "react";
import { ZipIcon } from "@/icons";
import CreateProjectModal from "./create-project-modal";
import AddFromEnvModal from "./add-from-env";
import { ScrollArea } from "@/components/ui/scroll-area";

interface AuditWithCodeProps {
  onNext?: () => void;
  type?: "zip" | "smart-address";
}

export default function AuditWithCode({ onNext, type = "zip" }: AuditWithCodeProps) {
  const [envVars, setEnvVars] = useState([{ name: "", value: "" }]);

  const addEnvVar = () => {
    setEnvVars([...envVars, { name: "", value: "" }]);
  };

  const removeEnvVar = (index: number) => {
    setEnvVars(envVars.filter((_, i) => i !== index));
  };

  const updateEnvVar = (index: number, field: "name" | "value", value: string) => {
    const updated = envVars.map((env, i) => (i === index ? { ...env, [field]: value } : env));
    setEnvVars(updated);
  };
  return (
    <ScrollArea className="h-[calc(100vh-200px)] px-[100px] py-5 ">
      <div className="h-full">
      {/* Header */}
      <div className="space-y-3 mb-6">
        <h1 className="text-2xl font-semibold text-foreground">
          {type === "smart-address" ? "Audit with smart address" : "Audit with code zip"}
        </h1>
        <p className="text-sm text-foreground/80 leading-relaxed">
          You seem to be using <span className="font-medium">Node</span>, so we've autofilled some fields accordingly.
          Make sure the values look right to you!
        </p>
      </div>

      <div className="space-y-6 pb-20">
        {/* Source Code or Contract Details */}
        {type === "smart-address" ? (
          <>
            {/* Contract address */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
              <Label className="text-sm font-medium md:text-right">Contract address</Label>
              <div className="md:col-span-3 flex items-center gap-3">
                <div className="flex-1 flex items-center gap-2 px-3 py-1 border rounded-md bg-background">
                  <span className="text-sm">0x204562...54846e</span>
                  <Button variant={"ghost"} className="ml-auto">
                    Edit
                  </Button>
                </div>
                <Button variant="default" size="lg">
                  Verify
                </Button>
              </div>
            </div>

            {/* Network Name */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
              <Label className="text-sm font-medium md:text-right">Network Name</Label>
              <div className="md:col-span-3">
                <Input placeholder="E.g. Ethereum, Sepolia, Polygon" className="w-full" />
              </div>
            </div>
          </>
        ) : (
          /* Source Code */
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
            <Label className="text-sm font-medium md:text-right">Source Code</Label>
            <div className="md:col-span-3 flex items-center gap-3">
              <div className="flex-1 flex items-center gap-2 px-3 py-2 border rounded-md bg-muted">
                <ZipIcon />
                <span className="text-sm">pedalsup / node-file</span>
                <Button variant="ghost" className="ml-auto">
                  Edit
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Name */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
          <Label htmlFor="name" className="text-sm font-medium md:text-right">
            Name
          </Label>
          <div className="md:col-span-3">
            <Input id="name" defaultValue="project node file" className="w-full" />
          </div>
        </div>

        {/* Project */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-3 items-start">
          <div className="flex items-center gap-3">
            <Label>Project</Label>
            <Badge variant="outline" className="border-none">
              Optional
            </Badge>
          </div>
          <div className="md:col-span-3">
            <Card className="bg-background py-3">
              <CardHeader className="px-3">
                <CardTitle className="text-lg font-normal">Create a new project to add this to?</CardTitle>
                <CardDescription className="text-sm font-normal text-foreground/80">
                  You don’t have any projects in this workspace. Projects allow you to group resources into environments
                  so you can better manage related resources.
                </CardDescription>
              </CardHeader>
              <CardContent className="px-3">
                <div className="space-y-3">
                  <div className="flex gap-2 pt-1 items-center">
                    <CreateProjectModal />
                    <span className="text-foreground/80 text-xs">or</span>
                    <Button variant="outline" size="sm" className="text-sm bg-transparent">
                      Select existing project
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Language */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
          <Label className="text-sm font-medium md:text-right">Language</Label>
          <div className="md:col-span-3">
            <Select defaultValue="solidity">
              <SelectTrigger className="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="solidity">Solidity</SelectItem>
                <SelectItem value="javascript">JavaScript</SelectItem>
                <SelectItem value="typescript">TypeScript</SelectItem>
                <SelectItem value="python">Python</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Version */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
          <Label className="text-sm font-medium md:text-right">Version</Label>
          <div className="md:col-span-3">
            <Select defaultValue="0.8.16">
              <SelectTrigger className="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0.8.16">0.8.16</SelectItem>
                <SelectItem value="0.8.15">0.8.15</SelectItem>
                <SelectItem value="0.8.14">0.8.14</SelectItem>
                <SelectItem value="0.8.13">0.8.13</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Compilation Framework */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
          <Label className="text-sm font-medium md:text-right">Compilation Framework</Label>
          <div className="md:col-span-3">
            <Select>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="e.g. Hardhat" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="hardhat">Hardhat</SelectItem>
                <SelectItem value="truffle">Truffle</SelectItem>
                <SelectItem value="foundry">Foundry</SelectItem>
                <SelectItem value="remix">Remix</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Environment Variables */}
        <div className="grid grid-cols-1">
          <Card className="bg-white">
            <CardHeader className="pb-4">
              <CardTitle>Environment Variables</CardTitle>
              <CardDescription className="text-primary">
                Set environment-specific config and secrets (such as API keys), then read those values from your code.{" "}
                <span>Learn more</span>.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {envVars.map((envVar, index) => (
                <div key={index} className="flex gap-2 items-center">
                  <Input
                    placeholder="NAME_OF_VARIABLE"
                    className="flex-1 text-sm"
                    value={envVar.name}
                    onChange={(e) => updateEnvVar(index, "name", e.target.value)}
                  />
                  <Input
                    placeholder="value"
                    className="flex-1 text-sm"
                    value={envVar.value}
                    onChange={(e) => updateEnvVar(index, "value", e.target.value)}
                  />
                  <Button variant="ghost">
                    Generate
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="shrink-0"
                    onClick={() => removeEnvVar(index)}
                    disabled={envVars.length === 1}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              <div className="flex gap-2 pt-2">
                <Button variant="outline" size="sm" onClick={addEnvVar}>
                  <Plus className="h-4 w-4 mr-1" />
                  Add Environment Variable
                </Button>
                <AddFromEnvModal />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Start Scan Button */}
        <div className="grid grid-cols-1 items-start">
          <div className="md:col-span-3">
            <Button onClick={onNext}>Start Scan</Button>
          </div>
        </div>
      </div>
    </div>
    </ScrollArea>
  );
}
